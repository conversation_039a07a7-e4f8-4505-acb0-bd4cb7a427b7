{"name": "functions", "main": "dist/index.js", "private": true, "engineStrict": true, "sideEffects": false, "type": "module", "engines": {"node": ">=22", "npm": ">=10", "pnpm": "please-use-npm", "yarn": "please-use-npm"}, "scripts": {"lint": "echo Skipping", "build:report-generation": "cd report-generation && npm run build", "build:functions": "rm -rf dist && node ./esbuild.config.js", "build": "npm run build:report-generation && npm run build:functions", "build:watch": "tsc --watch", "typecheck": "tsc --noEmit", "test": "cross-env FIREBASE_CONFIG=fly-fit-dev bun test", "test:ci": "bun run test", "test:only": "cross-env FIREBASE_CONFIG=fly-fit-dev bun test firestore --only", "serve": "bun ./scripts/serve-emulator-with-mock-data.zx.mjs", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy:dev:user": "firebase deploy --only functions:onAppUserUpdate", "deploy:dev": "firebase deploy --only functions,firestore --project fly-fit-dev", "deploy:prod": "firebase deploy --only functions,firestore --project fly-fit", "logs": "firebase functions:log", "email": "email dev"}, "dependencies": {"@fly-fit/shared-types": "workspace:*", "@react-email/components": "0.0.41", "algoliasearch": "^5.25.0", "async-retry": "^1.3.3", "cheerio": "^1.0.0", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dequal": "^2.0.3", "dotenv": "^16.5.0", "expo-server-sdk": "^3.15.0", "firebase": "^11.6.1", "firebase-admin": "^13.3.0", "firebase-functions": "^6.3.2", "googleapis": "^148.0.0", "luxon": "^3.6.1", "p-limit": "^6.2.0", "p-queue": "^8.1.0", "react": "^19.1.0", "react-email": "^4.0.15", "resend": "^4.5.1", "ts-pattern": "^5.7.0", "uuid": "^11.1.0"}, "devDependencies": {"@google-cloud/firestore": "^7.11.0", "@google-cloud/storage": "^7.16.0", "@types/async-retry": "^1.4.9", "axios": "^1.9.0", "cross-env": "^7.0.3", "esbuild": "^0.25.3", "firebase-functions-test": "^3.4.1", "typescript": "~5.8.3"}}