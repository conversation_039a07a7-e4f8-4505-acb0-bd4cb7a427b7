{"5528382556457213313web-sign-up/vite.config.ts": {"targets": {"build": {"command": "vite build", "options": {"cwd": "web-sign-up"}, "cache": true, "dependsOn": ["^build"], "inputs": ["default", "^default", {"externalDependencies": ["vite"]}], "outputs": ["{projectRoot}/dist-dev"], "metadata": {"technologies": ["vite"], "description": "Run Vite build", "help": {"command": "npx vite build --help", "example": {"options": {"sourcemap": true, "manifest": "manifest.json"}}}}}, "serve": {"continuous": true, "command": "vite", "options": {"cwd": "web-sign-up"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}, "deprecated": "Use devTargetName instead. This option will be removed in Nx 22."}}, "dev": {"continuous": true, "command": "vite", "options": {"cwd": "web-sign-up"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}}}, "preview": {"continuous": true, "command": "vite preview", "dependsOn": ["build"], "options": {"cwd": "web-sign-up"}, "metadata": {"technologies": ["vite"], "description": "Locally preview Vite production build", "help": {"command": "npx vite preview --help", "example": {"options": {"port": 3000}}}}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}}, "typecheck": {"cache": true, "inputs": ["default", "^default", {"externalDependencies": ["typescript"]}], "command": "tsc --noEmit -p tsconfig.app.json", "options": {"cwd": "web-sign-up"}, "metadata": {"description": "Runs type-checking for the project.", "technologies": ["typescript"], "help": {"command": "npx tsc -p tsconfig.app.json --help", "example": {"options": {"noEmit": true}}}}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects web-sign-up --includeDependentProjects -- npx nx build-deps web-sign-up"}}, "metadata": {}, "projectType": "application"}, "2602052417653182450functions/report-generation/vite.config.ts": {"targets": {"build": {"command": "vite build", "options": {"cwd": "functions/report-generation"}, "cache": true, "dependsOn": ["^build"], "inputs": ["default", "^default", {"externalDependencies": ["vite"]}], "outputs": ["{projectRoot}/dist"], "metadata": {"technologies": ["vite"], "description": "Run Vite build", "help": {"command": "npx vite build --help", "example": {"options": {"sourcemap": true, "manifest": "manifest.json"}}}}}, "serve": {"continuous": true, "command": "vite", "options": {"cwd": "functions/report-generation"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}, "deprecated": "Use devTargetName instead. This option will be removed in Nx 22."}}, "dev": {"continuous": true, "command": "vite", "options": {"cwd": "functions/report-generation"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}}}, "preview": {"continuous": true, "command": "vite preview", "dependsOn": ["build"], "options": {"cwd": "functions/report-generation"}, "metadata": {"technologies": ["vite"], "description": "Locally preview Vite production build", "help": {"command": "npx vite preview --help", "example": {"options": {"port": 3000}}}}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}}, "typecheck": {"cache": true, "inputs": ["default", "^default", {"externalDependencies": ["typescript"]}], "command": "tsc --noEmit -p tsconfig.json", "options": {"cwd": "functions/report-generation"}, "metadata": {"description": "Runs type-checking for the project.", "technologies": ["typescript"], "help": {"command": "npx tsc -p tsconfig.json --help", "example": {"options": {"noEmit": true}}}}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects report-generation --includeDependentProjects -- npx nx build-deps report-generation"}}, "metadata": {}, "projectType": "application"}, "881418513917698975functions/report-generation/vite.config.ts": {"targets": {"build": {"command": "vite build", "options": {"cwd": "functions/report-generation"}, "cache": true, "dependsOn": ["^build"], "inputs": ["default", "^default", {"externalDependencies": ["vite"]}], "outputs": ["{projectRoot}/dist"], "metadata": {"technologies": ["vite"], "description": "Run Vite build", "help": {"command": "npx vite build --help", "example": {"options": {"sourcemap": true, "manifest": "manifest.json"}}}}}, "serve": {"continuous": true, "command": "vite", "options": {"cwd": "functions/report-generation"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}, "deprecated": "Use devTargetName instead. This option will be removed in Nx 22."}}, "dev": {"continuous": true, "command": "vite", "options": {"cwd": "functions/report-generation"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}}}, "preview": {"continuous": true, "command": "vite preview", "dependsOn": ["build"], "options": {"cwd": "functions/report-generation"}, "metadata": {"technologies": ["vite"], "description": "Locally preview Vite production build", "help": {"command": "npx vite preview --help", "example": {"options": {"port": 3000}}}}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}}, "typecheck": {"cache": true, "inputs": ["default", "^default", {"externalDependencies": ["typescript"]}], "command": "tsc --noEmit -p tsconfig.json", "options": {"cwd": "functions/report-generation"}, "metadata": {"description": "Runs type-checking for the project.", "technologies": ["typescript"], "help": {"command": "npx tsc -p tsconfig.json --help", "example": {"options": {"noEmit": true}}}}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects report-generation --includeDependentProjects -- npx nx build-deps report-generation"}}, "metadata": {}, "projectType": "application"}, "3082662818769913625web-sign-up/vite.config.ts": {"targets": {"build": {"command": "vite build", "options": {"cwd": "web-sign-up"}, "cache": true, "dependsOn": ["^build"], "inputs": ["default", "^default", {"externalDependencies": ["vite"]}], "outputs": ["{projectRoot}/dist-dev"], "metadata": {"technologies": ["vite"], "description": "Run Vite build", "help": {"command": "npx vite build --help", "example": {"options": {"sourcemap": true, "manifest": "manifest.json"}}}}}, "serve": {"continuous": true, "command": "vite", "options": {"cwd": "web-sign-up"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}, "deprecated": "Use devTargetName instead. This option will be removed in Nx 22."}}, "dev": {"continuous": true, "command": "vite", "options": {"cwd": "web-sign-up"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}}}, "preview": {"continuous": true, "command": "vite preview", "dependsOn": ["build"], "options": {"cwd": "web-sign-up"}, "metadata": {"technologies": ["vite"], "description": "Locally preview Vite production build", "help": {"command": "npx vite preview --help", "example": {"options": {"port": 3000}}}}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}}, "typecheck": {"cache": true, "inputs": ["default", "^default", {"externalDependencies": ["typescript"]}], "command": "tsc --noEmit -p tsconfig.app.json", "options": {"cwd": "web-sign-up"}, "metadata": {"description": "Runs type-checking for the project.", "technologies": ["typescript"], "help": {"command": "npx tsc -p tsconfig.app.json --help", "example": {"options": {"noEmit": true}}}}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects web-sign-up --includeDependentProjects -- npx nx build-deps web-sign-up"}}, "metadata": {}, "projectType": "application"}, "17079639128902583980functions/report-generation/vite.config.ts": {"targets": {"build": {"command": "vite build", "options": {"cwd": "functions/report-generation"}, "cache": true, "dependsOn": ["^build"], "inputs": ["default", "^default", {"externalDependencies": ["vite"]}], "outputs": ["{projectRoot}/dist"], "metadata": {"technologies": ["vite"], "description": "Run Vite build", "help": {"command": "npx vite build --help", "example": {"options": {"sourcemap": true, "manifest": "manifest.json"}}}}}, "serve": {"continuous": true, "command": "vite", "options": {"cwd": "functions/report-generation"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}, "deprecated": "Use devTargetName instead. This option will be removed in Nx 22."}}, "dev": {"continuous": true, "command": "vite", "options": {"cwd": "functions/report-generation"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}}}, "preview": {"continuous": true, "command": "vite preview", "dependsOn": ["build"], "options": {"cwd": "functions/report-generation"}, "metadata": {"technologies": ["vite"], "description": "Locally preview Vite production build", "help": {"command": "npx vite preview --help", "example": {"options": {"port": 3000}}}}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}}, "typecheck": {"cache": true, "inputs": ["default", "^default", {"externalDependencies": ["typescript"]}], "command": "tsc --noEmit -p tsconfig.json", "options": {"cwd": "functions/report-generation"}, "metadata": {"description": "Runs type-checking for the project.", "technologies": ["typescript"], "help": {"command": "npx tsc -p tsconfig.json --help", "example": {"options": {"noEmit": true}}}}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects report-generation --includeDependentProjects -- npx nx build-deps report-generation"}}, "metadata": {}, "projectType": "application"}, "182028816570363625web-sign-up/vite.config.ts": {"targets": {"build": {"command": "vite build", "options": {"cwd": "web-sign-up"}, "cache": true, "dependsOn": ["^build"], "inputs": ["default", "^default", {"externalDependencies": ["vite"]}], "outputs": ["{projectRoot}/dist-dev"], "metadata": {"technologies": ["vite"], "description": "Run Vite build", "help": {"command": "npx vite build --help", "example": {"options": {"sourcemap": true, "manifest": "manifest.json"}}}}}, "serve": {"continuous": true, "command": "vite", "options": {"cwd": "web-sign-up"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}, "deprecated": "Use devTargetName instead. This option will be removed in Nx 22."}}, "dev": {"continuous": true, "command": "vite", "options": {"cwd": "web-sign-up"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}}}, "preview": {"continuous": true, "command": "vite preview", "dependsOn": ["build"], "options": {"cwd": "web-sign-up"}, "metadata": {"technologies": ["vite"], "description": "Locally preview Vite production build", "help": {"command": "npx vite preview --help", "example": {"options": {"port": 3000}}}}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}}, "typecheck": {"cache": true, "inputs": ["default", "^default", {"externalDependencies": ["typescript"]}], "command": "tsc --noEmit -p tsconfig.app.json", "options": {"cwd": "web-sign-up"}, "metadata": {"description": "Runs type-checking for the project.", "technologies": ["typescript"], "help": {"command": "npx tsc -p tsconfig.app.json --help", "example": {"options": {"noEmit": true}}}}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects web-sign-up --includeDependentProjects -- npx nx build-deps web-sign-up"}}, "metadata": {}, "projectType": "application"}, "4628154525174379234web-sign-up/vite.config.ts": {"targets": {"build": {"command": "vite build", "options": {"cwd": "web-sign-up"}, "cache": true, "dependsOn": ["^build"], "inputs": ["default", "^default", {"externalDependencies": ["vite"]}], "outputs": ["{projectRoot}/dist-dev"], "metadata": {"technologies": ["vite"], "description": "Run Vite build", "help": {"command": "npx vite build --help", "example": {"options": {"sourcemap": true, "manifest": "manifest.json"}}}}}, "serve": {"continuous": true, "command": "vite", "options": {"cwd": "web-sign-up"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}, "deprecated": "Use devTargetName instead. This option will be removed in Nx 22."}}, "dev": {"continuous": true, "command": "vite", "options": {"cwd": "web-sign-up"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}}}, "preview": {"continuous": true, "command": "vite preview", "dependsOn": ["build"], "options": {"cwd": "web-sign-up"}, "metadata": {"technologies": ["vite"], "description": "Locally preview Vite production build", "help": {"command": "npx vite preview --help", "example": {"options": {"port": 3000}}}}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}}, "typecheck": {"cache": true, "inputs": ["default", "^default", {"externalDependencies": ["typescript"]}], "command": "tsc --noEmit -p tsconfig.app.json", "options": {"cwd": "web-sign-up"}, "metadata": {"description": "Runs type-checking for the project.", "technologies": ["typescript"], "help": {"command": "npx tsc -p tsconfig.app.json --help", "example": {"options": {"noEmit": true}}}}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects web-sign-up --includeDependentProjects -- npx nx build-deps web-sign-up"}}, "metadata": {}, "projectType": "application"}, "16394330398106073527functions/report-generation/vite.config.ts": {"targets": {"build": {"command": "vite build", "options": {"cwd": "functions/report-generation"}, "cache": true, "dependsOn": ["^build"], "inputs": ["default", "^default", {"externalDependencies": ["vite"]}], "outputs": ["{projectRoot}/dist"], "metadata": {"technologies": ["vite"], "description": "Run Vite build", "help": {"command": "npx vite build --help", "example": {"options": {"sourcemap": true, "manifest": "manifest.json"}}}}}, "serve": {"continuous": true, "command": "vite", "options": {"cwd": "functions/report-generation"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}, "deprecated": "Use devTargetName instead. This option will be removed in Nx 22."}}, "dev": {"continuous": true, "command": "vite", "options": {"cwd": "functions/report-generation"}, "metadata": {"technologies": ["vite"], "description": "Starts Vite dev server", "help": {"command": "npx vite --help", "example": {"options": {"port": 3000}}}}}, "preview": {"continuous": true, "command": "vite preview", "dependsOn": ["build"], "options": {"cwd": "functions/report-generation"}, "metadata": {"technologies": ["vite"], "description": "Locally preview Vite production build", "help": {"command": "npx vite preview --help", "example": {"options": {"port": 3000}}}}}, "serve-static": {"continuous": true, "executor": "@nx/web:file-server", "options": {"buildTarget": "build", "spa": true}}, "typecheck": {"cache": true, "inputs": ["default", "^default", {"externalDependencies": ["typescript"]}], "command": "tsc --noEmit -p tsconfig.json", "options": {"cwd": "functions/report-generation"}, "metadata": {"description": "Runs type-checking for the project.", "technologies": ["typescript"], "help": {"command": "npx tsc -p tsconfig.json --help", "example": {"options": {"noEmit": true}}}}}, "build-deps": {"dependsOn": ["^build"]}, "watch-deps": {"continuous": true, "dependsOn": ["build-deps"], "command": "npx nx watch --projects report-generation --includeDependentProjects -- npx nx build-deps report-generation"}}, "metadata": {}, "projectType": "application"}}