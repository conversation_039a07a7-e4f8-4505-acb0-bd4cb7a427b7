{"17444992884142623230": {}, "4138722719448260562": {".": {"targets": {"eslint:lint": {"command": "eslint ./src", "cache": true, "options": {"cwd": "."}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "10938270507114712102": {"functions": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "functions"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "6370791894666261367": {"web-sign-up": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "web-sign-up"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "9469960350847202070": {"functions/report-generation": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "functions/report-generation"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "4518281915117736268": {".": {"targets": {"eslint:lint": {"command": "eslint ./src", "cache": true, "options": {"cwd": "."}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "4184001392062274090": {".": {"targets": {"eslint:lint": {"command": "eslint ./src", "cache": true, "options": {"cwd": "."}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "3409901595829500440": {".": {"targets": {"eslint:lint": {"command": "eslint ./src", "cache": true, "options": {"cwd": "."}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "6286668699622987658": {".": {"targets": {"eslint:lint": {"command": "eslint ./src", "cache": true, "options": {"cwd": "."}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "5480156713830524491": {".": {"targets": {"eslint:lint": {"command": "eslint ./src", "cache": true, "options": {"cwd": "."}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "2598342308467640588": {".": {"targets": {"eslint:lint": {"command": "eslint ./src", "cache": true, "options": {"cwd": "."}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "2011349146650953033": {".": {"targets": {"eslint:lint": {"command": "eslint ./src", "cache": true, "options": {"cwd": "."}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "11351318770459191909": {".": {"targets": {"eslint:lint": {"command": "eslint ./src", "cache": true, "options": {"cwd": "."}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "5403778315354153589": {"functions": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "functions"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "11210836885581209693": {"functions/report-generation": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "functions/report-generation"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "3419669647788092558": {"web-sign-up": {"targets": {"eslint:lint": {"command": "eslint .", "cache": true, "options": {"cwd": "web-sign-up"}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "10291503231662983361": {".": {"targets": {"eslint:lint": {"command": "eslint ./src", "cache": true, "options": {"cwd": "."}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "15501331191316140492": {".": {"targets": {"eslint:lint": {"command": "eslint ./src", "cache": true, "options": {"cwd": "."}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "12018137721906388620": {".": {"targets": {"eslint:lint": {"command": "eslint ./src", "cache": true, "options": {"cwd": "."}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "8555066539119090299": {".": {"targets": {"eslint:lint": {"command": "eslint ./src", "cache": true, "options": {"cwd": "."}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "12837588297297707167": {".": {"targets": {"eslint:lint": {"command": "eslint ./src", "cache": true, "options": {"cwd": "."}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "1948279017417040180": {".": {"targets": {"eslint:lint": {"command": "eslint ./src", "cache": true, "options": {"cwd": "."}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "14010191142615777731": {".": {"targets": {"eslint:lint": {"command": "eslint ./src", "cache": true, "options": {"cwd": "."}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "7340838423215596953": {".": {"targets": {"eslint:lint": {"command": "eslint ./src", "cache": true, "options": {"cwd": "."}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "14940086979059457067": {".": {"targets": {"eslint:lint": {"command": "eslint ./src", "cache": true, "options": {"cwd": "."}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "9109757694796533726": {".": {"targets": {"eslint:lint": {"command": "eslint ./src", "cache": true, "options": {"cwd": "."}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "12827644469936556413": {".": {"targets": {"eslint:lint": {"command": "eslint ./src", "cache": true, "options": {"cwd": "."}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "1659551895309875021": {".": {"targets": {"eslint:lint": {"command": "eslint ./src", "cache": true, "options": {"cwd": "."}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}, "9418360098082232245": {".": {"targets": {"eslint:lint": {"command": "eslint ./src", "cache": true, "options": {"cwd": "."}, "inputs": ["default", "^default", "{workspaceRoot}/eslint.config.mjs", "{workspaceRoot}/tools/eslint-rules/**/*", {"externalDependencies": ["eslint"]}], "outputs": ["{options.outputFile}"], "metadata": {"technologies": ["eslint"], "description": "Runs ESLint on project", "help": {"command": "npx eslint --help", "example": {"options": {"max-warnings": 0}}}}}}}}}