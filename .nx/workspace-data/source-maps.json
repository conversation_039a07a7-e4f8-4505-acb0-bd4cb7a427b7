{"functions": {"root": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.cache": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.options": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.inputs": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.outputs": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.executor": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.options.cwd": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.options.command": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata.technologies": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata.technologies.0": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata.description": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata.help": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata.help.command": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata.help.example": ["eslint.config.mjs", "@nx/eslint/plugin"]}, "functions/report-generation": {"root": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.cache": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.options": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.inputs": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.outputs": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.executor": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.options.cwd": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.options.command": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata.technologies": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata.technologies.0": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata.description": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata.help": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata.help.command": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata.help.example": ["eslint.config.mjs", "@nx/eslint/plugin"], "projectType": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.build": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.build.options": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.build.cache": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.build.dependsOn": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.build.inputs": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.build.outputs": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.build.metadata": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.build.executor": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.build.options.cwd": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.build.options.command": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.build.metadata.technologies": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.build.metadata.technologies.0": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.build.metadata.description": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.build.metadata.help": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.build.metadata.help.command": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.build.metadata.help.example": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.serve": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.serve.continuous": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.serve.options": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.serve.executor": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.serve.options.cwd": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.serve.options.command": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.technologies": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.technologies.0": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.description": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.help": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.help.command": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.help.example": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.deprecated": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.dev": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.dev.continuous": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.dev.options": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.dev.executor": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.dev.options.cwd": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.dev.options.command": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata.technologies": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata.technologies.0": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata.description": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata.help": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata.help.command": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata.help.example": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.preview": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.preview.continuous": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.preview.dependsOn": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.preview.options": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.preview.executor": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.preview.options.cwd": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.preview.options.command": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata.technologies": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata.technologies.0": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata.description": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata.help": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata.help.command": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata.help.example": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.serve-static": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.serve-static.continuous": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.serve-static.executor": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.serve-static.options": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.serve-static.options.buildTarget": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.serve-static.options.spa": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.cache": ["nx.json", "nx/target-defaults"], "targets.typecheck.inputs": ["nx.json", "nx/target-defaults"], "targets.typecheck.options": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.metadata": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.executor": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.options.cwd": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.options.command": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.metadata.description": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.metadata.technologies": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.metadata.technologies.0": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.metadata.help": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.metadata.help.command": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.metadata.help.example": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.build-deps": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.build-deps.dependsOn": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.watch-deps": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.watch-deps.continuous": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.watch-deps.dependsOn": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.watch-deps.executor": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.watch-deps.options": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.watch-deps.options.command": ["functions/report-generation/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.outputs": ["nx.json", "nx/target-defaults"], "targets.typecheck.parallelism": ["nx.json", "nx/target-defaults"]}, ".": {"root": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.cache": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.options": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.inputs": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.outputs": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.executor": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.options.cwd": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.options.command": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata.technologies": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata.technologies.0": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata.description": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata.help": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata.help.command": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata.help.example": ["eslint.config.mjs", "@nx/eslint/plugin"]}, "web-sign-up": {"root": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.cache": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.options": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.inputs": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.outputs": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.executor": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.options.cwd": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.options.command": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata.technologies": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata.technologies.0": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata.description": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata.help": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata.help.command": ["eslint.config.mjs", "@nx/eslint/plugin"], "targets.eslint:lint.metadata.help.example": ["eslint.config.mjs", "@nx/eslint/plugin"], "projectType": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.build": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.build.options": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.build.cache": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.build.dependsOn": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.build.inputs": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.build.outputs": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.build.metadata": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.build.executor": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.build.options.cwd": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.build.options.command": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.build.metadata.technologies": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.build.metadata.technologies.0": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.build.metadata.description": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.build.metadata.help": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.build.metadata.help.command": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.build.metadata.help.example": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.serve": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.serve.continuous": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.serve.options": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.serve.executor": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.serve.options.cwd": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.serve.options.command": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.technologies": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.technologies.0": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.description": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.help": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.help.command": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.help.example": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.serve.metadata.deprecated": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.dev": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.dev.continuous": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.dev.options": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.dev.executor": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.dev.options.cwd": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.dev.options.command": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata.technologies": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata.technologies.0": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata.description": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata.help": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata.help.command": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.dev.metadata.help.example": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.preview": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.preview.continuous": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.preview.dependsOn": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.preview.options": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.preview.executor": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.preview.options.cwd": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.preview.options.command": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata.technologies": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata.technologies.0": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata.description": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata.help": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata.help.command": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.preview.metadata.help.example": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.serve-static": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.serve-static.continuous": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.serve-static.executor": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.serve-static.options": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.serve-static.options.buildTarget": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.serve-static.options.spa": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.cache": ["nx.json", "nx/target-defaults"], "targets.typecheck.inputs": ["nx.json", "nx/target-defaults"], "targets.typecheck.options": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.metadata": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.executor": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.options.cwd": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.options.command": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.metadata.description": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.metadata.technologies": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.metadata.technologies.0": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.metadata.help": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.metadata.help.command": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.metadata.help.example": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.build-deps": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.build-deps.dependsOn": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.watch-deps": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.watch-deps.continuous": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.watch-deps.dependsOn": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.watch-deps.executor": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.watch-deps.options": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.watch-deps.options.command": ["web-sign-up/vite.config.ts", "@nx/vite/plugin"], "targets.typecheck.outputs": ["nx.json", "nx/target-defaults"], "targets.typecheck.parallelism": ["nx.json", "nx/target-defaults"]}}