{"14996464022998485009": {"targets": {"test": {"command": "jest", "options": {"cwd": ".", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\",\"module\":\"commonjs\",\"customConditions\":null}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^default", {"externalDependencies": ["jest", "jest-expo/web"]}], "outputs": []}}}}