{"version": "6.0", "nxVersion": "21.1.2", "pathMappings": {"@api": ["./src/api/index.ts"], "@assets": ["./src/assets/index.ts"], "@backend": ["./src/utils/firestore/firebase.config.ts"], "@base-components": ["./src/base-components/index.ts"], "@components": ["./src/components/index.ts"], "@constants": ["./src/constants/index.ts"], "@contexts": ["./src/contexts/index.ts"], "@data-hooks": ["./src/data-hooks/index.ts"], "@env": ["./src/types/env.d.ts"], "@hooks": ["./src/hooks/index.ts"], "@navigation": ["./src/utils/navigation/index.ts"], "@pages": ["./src/pages/index.ts"], "@types": ["./src/types/index.ts"], "@utils": ["./src/utils/index.ts"]}, "nxJsonPlugins": [{"name": "@nx/eslint/plugin", "options": {"targetName": "eslint:lint"}}, {"name": "@nx/jest/plugin", "options": {"targetName": "jest:test"}}, {"name": "@nx/expo/plugin", "options": {"startTargetName": "start", "buildTargetName": "build", "prebuildTargetName": "prebuild", "serveTargetName": "serve", "installTargetName": "install", "exportTargetName": "export", "submitTargetName": "submit", "runIosTargetName": "run-ios", "runAndroidTargetName": "run-android", "buildDepsTargetName": "build-deps", "watchDepsTargetName": "watch-deps"}}, {"name": "@nx/react-native/plugin", "options": {"startTargetName": "start", "upgradeTargetName": "update", "bundleTargetName": "bundle", "podInstallTargetName": "pod-install", "runIosTargetName": "run-ios", "runAndroidTargetName": "run-android", "buildIosTargetName": "build-ios", "buildAndroidTargetName": "build-android", "syncDepsTargetName": "sync-deps"}}, {"name": "@nx/vite/plugin", "options": {"buildTargetName": "build", "testTargetName": "test", "serveTargetName": "serve", "devTargetName": "dev", "previewTargetName": "preview", "serveStaticTargetName": "serve-static", "typecheckTargetName": "typecheck", "buildDepsTargetName": "build-deps", "watchDepsTargetName": "watch-deps"}}], "fileMap": {"nonProjectFiles": [], "projectFileMap": {"report-generation": [{"file": "functions/report-generation/.npmrc", "hash": "13394946703242683089"}, {"file": "functions/report-generation/README.md", "hash": "8593264683175004647"}, {"file": "functions/report-generation/esbuild.cli.config.js", "hash": "5317759641256882357", "deps": ["npm:esbuild"]}, {"file": "functions/report-generation/esbuild.config.js", "hash": "12108582049698708685", "deps": ["npm:esbuild"]}, {"file": "functions/report-generation/index.html", "hash": "5726984467505585633"}, {"file": "functions/report-generation/package-lock.json", "hash": "6685363259048387272"}, {"file": "functions/report-generation/package.json", "hash": "17760990529829156284", "deps": ["npm:@types/node", "npm:@types/react", "npm:esbuild", "npm:mkdirp@3.0.1", "npm:open", "npm:typescript", "npm:vite", "npm:axios", "npm:date-fns", "npm:jotai", "npm:react", "npm:react-dom"]}, {"file": "functions/report-generation/src/Cli.tsx", "hash": "11721769507709425994"}, {"file": "functions/report-generation/src/Node.tsx", "hash": "10848648806520010752"}, {"file": "functions/report-generation/src/Web.tsx", "hash": "7750289836162751732", "deps": ["npm:jotai", "npm:react", "npm:react-dom"]}, {"file": "functions/report-generation/src/fonts/Lato-Bold.ttf", "hash": "6314020571814602440"}, {"file": "functions/report-generation/src/fonts/Lato-Regular.ttf", "hash": "7595113985758757429"}, {"file": "functions/report-generation/src/fonts/LibreBaskerville-Bold.ttf", "hash": "4467128957493173214"}, {"file": "functions/report-generation/src/fonts/LibreBaskerville-Regular.ttf", "hash": "9234073623294482512"}, {"file": "functions/report-generation/src/hooks/dataAtom.ts", "hash": "13412086773236505662", "deps": ["npm:jotai"]}, {"file": "functions/report-generation/src/hooks/dataFetch.ts", "hash": "5181227959157575846", "deps": ["npm:axios"]}, {"file": "functions/report-generation/src/hooks/index.ts", "hash": "16099762963095798834"}, {"file": "functions/report-generation/src/media/FlyBodies.png", "hash": "3813414014003008043"}, {"file": "functions/report-generation/src/media/arctic_tern7x5.jpg", "hash": "3377477364848626637"}, {"file": "functions/report-generation/src/media/bar_tailed_godwit7x5.jpg", "hash": "2395841818682327453"}, {"file": "functions/report-generation/src/media/short_tailed_shearwater7x5.jpg", "hash": "4705684523577417563"}, {"file": "functions/report-generation/src/media/sooty_shearwater7x5.jpg", "hash": "4178521873810064842"}, {"file": "functions/report-generation/src/media/wandering_albatross7x5.jpg", "hash": "10791786407440005453"}, {"file": "functions/report-generation/src/reports/HeaderContent.tsx", "hash": "15240890784852273981"}, {"file": "functions/report-generation/src/reports/LocalLoader.tsx", "hash": "8000475141129306702", "deps": ["npm:react"]}, {"file": "functions/report-generation/src/reports/SummaryReportComponent.tsx", "hash": "15079132870087188361"}, {"file": "functions/report-generation/src/reports/challenge/ChallengeCumulativeDailyDataChart.tsx", "hash": "13079065788002619284", "deps": ["npm:date-fns"]}, {"file": "functions/report-generation/src/reports/challenge/ChallengeParticipantChart.tsx", "hash": "14736738737126116217", "deps": ["npm:date-fns"]}, {"file": "functions/report-generation/src/reports/challenge/ChallengeReport.tsx", "hash": "2808508605270829425"}, {"file": "functions/report-generation/src/reports/challenge/DidYouKnowBox.tsx", "hash": "6596232872293032606"}, {"file": "functions/report-generation/src/reports/challenge/Fly5.tsx", "hash": "15105605547811142168"}, {"file": "functions/report-generation/src/reports/challenge/FlyBirdieBox.tsx", "hash": "1352230153872268342"}, {"file": "functions/report-generation/src/reports/challenge/QuadBubble.tsx", "hash": "8173992083264730116"}, {"file": "functions/report-generation/src/reports/challenge/index.ts", "hash": "11047741801172122321"}, {"file": "functions/report-generation/src/reports/index.ts", "hash": "7298780638716603746"}, {"file": "functions/report-generation/src/reports/sevenDay/DayBox.tsx", "hash": "16610987385060579804"}, {"file": "functions/report-generation/src/reports/sevenDay/SevenDayReport.tsx", "hash": "734090085225979285"}, {"file": "functions/report-generation/src/reports/sevenDay/SevenDaySummaryStats.tsx", "hash": "9557892138060508990"}, {"file": "functions/report-generation/src/reports/sevenDay/StatBox.tsx", "hash": "17760757008050609304"}, {"file": "functions/report-generation/src/reports/sevenDay/index.ts", "hash": "5174428971925833689"}, {"file": "functions/report-generation/src/reports/staticContent.ts", "hash": "1742068349025015198"}, {"file": "functions/report-generation/src/tw.ts", "hash": "12516899611039186518"}, {"file": "functions/report-generation/src/types.ts", "hash": "18208273506589338020", "deps": ["functions"]}, {"file": "functions/report-generation/src/utils/arrayHelpers.ts", "hash": "18049129017096074505"}, {"file": "functions/report-generation/src/utils/dateTimeConversions.ts", "hash": "17774291971181475163", "deps": ["npm:date-fns"]}, {"file": "functions/report-generation/src/utils/index.ts", "hash": "11725074901182789803"}, {"file": "functions/report-generation/src/utils/setup/cjs-shim.ts", "hash": "12183125106293231805"}, {"file": "functions/report-generation/src/utils/utils.ts", "hash": "561672987640936438"}, {"file": "functions/report-generation/src/vite-env.d.ts", "hash": "12946363841406869089"}, {"file": "functions/report-generation/tsconfig.build.json", "hash": "7973125823150944679"}, {"file": "functions/report-generation/tsconfig.json", "hash": "5175850270336423290"}, {"file": "functions/report-generation/tsconfig.node.json", "hash": "4208030242441905014"}, {"file": "functions/report-generation/vite.config.ts", "hash": "119688687976234816", "deps": ["npm:vite"]}], "functions": [{"file": "functions/.env.fly-fit", "hash": "8214885901705591931"}, {"file": "functions/.env.fly-fit-dev", "hash": "2851592630575070842"}, {"file": "functions/.gitignore", "hash": "7080444811012829751"}, {"file": "functions/.npmrc", "hash": "13394946703242683089"}, {"file": "functions/__tests__/firestore.test-exclude.ts", "hash": "2754209148498540980"}, {"file": "functions/__tests__/snapshots/appUserAfter.snapshot.json", "hash": "4813153345512404769"}, {"file": "functions/__tests__/snapshots/appUserBefore.snapshot.json", "hash": "4813153345512404769"}, {"file": "functions/__tests__/snapshots/appUsers.snapshot.json", "hash": "11837794370430261599"}, {"file": "functions/__tests__/snapshots/challenges.snapshot.json", "hash": "8590171811371522537"}, {"file": "functions/__tests__/snapshots/healthStatUpdates.snapshot.json", "hash": "2532572564776702624"}, {"file": "functions/__tests__/snapshots/workouts.snapshot.json", "hash": "9348475346752474460"}, {"file": "functions/emails/Components.tsx", "hash": "4088916747475352254", "deps": ["npm:react"]}, {"file": "functions/emails/LoginConfirmationEmailTemplate.tsx", "hash": "4065926035164637163"}, {"file": "functions/emails/LoginEmailTemplate.tsx", "hash": "15915735910347422762"}, {"file": "functions/emails/MileageChallengeReportEmailTemplate.tsx", "hash": "12838428670451280606"}, {"file": "functions/emails/SevenDayWellnessOutlookEmailTemplate.tsx", "hash": "17587523249132952869"}, {"file": "functions/emails/SevenDayWellnessReportEmailTemplate.tsx", "hash": "4812894168764226256"}, {"file": "functions/emails/StripeWelcomeEmail.tsx", "hash": "10072632289207396236"}, {"file": "functions/emails/TrackingDeviceConnectionEmailTemplate.tsx", "hash": "15109848958555533912"}, {"file": "functions/emails/WelcomeEmailTemplate.tsx", "hash": "17474800743193432480"}, {"file": "functions/emails/helpers.ts", "hash": "17170533791967125628"}, {"file": "functions/emails/images.ts", "hash": "6718373373985125322"}, {"file": "functions/emails/index.ts", "hash": "7642897401282230980"}, {"file": "functions/emails/static/stripe-logo.png", "hash": "620196823361583134"}, {"file": "functions/emails/tailwindConfig.ts", "hash": "11688889811184059370"}, {"file": "functions/emails/types.ts", "hash": "4809263974173772048"}, {"file": "functions/esbuild.config.js", "hash": "7531968971939442682", "deps": ["npm:esbuild"]}, {"file": "functions/linking-public/.well-known/apple-app-site-association", "hash": "10034810731097309166"}, {"file": "functions/linking-public/.well-known/assetlinks.json", "hash": "12048387748860272286"}, {"file": "functions/package-lock.json", "hash": "8468640969713060597"}, {"file": "functions/package.json", "hash": "4218244846228055095", "deps": ["npm:@google-cloud/firestore", "npm:@google-cloud/storage", "npm:axios", "npm:cross-env", "npm:esbuild", "npm:typescript", "npm:async-retry", "npm:date-fns", "npm:date-fns-tz", "npm:dequal", "npm:dotenv", "npm:firebase", "npm:firebase-admin", "npm:luxon", "npm:p-limit", "npm:react", "npm:ts-pattern", "npm:uuid"]}, {"file": "functions/scripts/serve-emulator-with-mock-data.zx.mjs", "hash": "17848342505749347191", "deps": ["npm:zx"]}, {"file": "functions/src/constants/domainConstants.ts", "hash": "3637953246219887210"}, {"file": "functions/src/constants/index.ts", "hash": "7674301121106142745"}, {"file": "functions/src/endpointHelpers/appUserHealthData.ts", "hash": "7577776052246602320"}, {"file": "functions/src/endpointHelpers/appUserHelpers.ts", "hash": "16354690813631369406"}, {"file": "functions/src/endpointHelpers/challengeCheckIn.ts", "hash": "6218413496340317866"}, {"file": "functions/src/endpointHelpers/challengeGroupAggregationCalculators.ts", "hash": "3375380176094856833"}, {"file": "functions/src/endpointHelpers/challengeGroupAggregationHelpers.ts", "hash": "14016290683537958752", "deps": ["npm:date-fns", "npm:firebase-admin"]}, {"file": "functions/src/endpointHelpers/challengeGroupHelpers.ts", "hash": "1844386866964212237", "deps": ["npm:express"]}, {"file": "functions/src/endpointHelpers/challengeHelpers.ts", "hash": "15939722035910325951"}, {"file": "functions/src/endpointHelpers/challengeImportHelpers.ts", "hash": "9711877975375234869", "deps": ["npm:firebase-admin"]}, {"file": "functions/src/endpointHelpers/challengePostHelpers.ts", "hash": "9444020480704335330"}, {"file": "functions/src/endpointHelpers/createUserHelpers.ts", "hash": "13472634697819103277", "deps": ["npm:date-fns-tz", "npm:express", "npm:firebase-admin"]}, {"file": "functions/src/endpointHelpers/debounceHelpers.ts", "hash": "9711079976411310788", "deps": ["npm:firebase-admin"]}, {"file": "functions/src/endpointHelpers/deleteUserHelpers.ts", "hash": "3679935658912163540", "deps": ["npm:firebase-admin"]}, {"file": "functions/src/endpointHelpers/emailActionHelpers.ts", "hash": "5134270871312453156"}, {"file": "functions/src/endpointHelpers/emailHelpers.ts", "hash": "11640989505322107730"}, {"file": "functions/src/endpointHelpers/fitbitHelpers.ts", "hash": "1219623339280013581"}, {"file": "functions/src/endpointHelpers/handleLeaveChallenge.ts", "hash": "675039742767487383", "deps": ["npm:express"]}, {"file": "functions/src/endpointHelpers/healthDataHelpers.ts", "hash": "9647032237185269849"}, {"file": "functions/src/endpointHelpers/index.ts", "hash": "18105652365411143353"}, {"file": "functions/src/endpointHelpers/isValidNewUserCredentialsHelpers.ts", "hash": "8526411373165381209", "deps": ["npm:express"]}, {"file": "functions/src/endpointHelpers/movementStreakHelpers.ts", "hash": "18136732886880255041"}, {"file": "functions/src/endpointHelpers/movementStreakPrimitiveHelpers.test-exclude.ts", "hash": "17464724390925660792"}, {"file": "functions/src/endpointHelpers/movementStreakPrimitiveHelpers.ts", "hash": "7391625404564077261"}, {"file": "functions/src/endpointHelpers/notificationHelpers.ts", "hash": "15459028864086444727", "deps": ["npm:firebase-admin"]}, {"file": "functions/src/endpointHelpers/organizationHelpers.ts", "hash": "10461749162801003903", "deps": ["npm:firebase-admin"]}, {"file": "functions/src/endpointHelpers/quizStreakHelpers.ts", "hash": "16804144898060191616"}, {"file": "functions/src/endpointHelpers/reportHelpers.ts", "hash": "12246642962808806694"}, {"file": "functions/src/endpoints/events/index.ts", "hash": "6815456611305797761"}, {"file": "functions/src/endpoints/events/onAppUserDocumentUpdate.ts", "hash": "11998649199237196264"}, {"file": "functions/src/endpoints/events/onAuthUserDelete.ts", "hash": "9553389256490789543"}, {"file": "functions/src/endpoints/events/onChallengeDocumentUpdated.ts", "hash": "17575981067514990205"}, {"file": "functions/src/endpoints/events/onChallengeGroupDocumentUpdate.ts", "hash": "7038256255888013501"}, {"file": "functions/src/endpoints/events/onChallengeGroupParticipantDocumentUpdate.ts", "hash": "15202039642138175751"}, {"file": "functions/src/endpoints/events/onChallengePostGroupsDocumentUpdate.ts", "hash": "336699093644996056"}, {"file": "functions/src/endpoints/events/onChallengePostTeamsDocumentUpdate.ts", "hash": "13115554799962277425"}, {"file": "functions/src/endpoints/events/onHealthDataDocumentUpdate.ts", "hash": "8561488686444967256"}, {"file": "functions/src/endpoints/events/onMovementStreakDocumentUpdate.ts", "hash": "1165198443824276376"}, {"file": "functions/src/endpoints/events/onOrganizationDocumentUpdated.ts", "hash": "11079510151238078553"}, {"file": "functions/src/endpoints/events/onQuizProgressUpdate.ts", "hash": "16040479077157550414"}, {"file": "functions/src/endpoints/events/onSummaryReportDocumentUpdate.ts", "hash": "14508626984790677684"}, {"file": "functions/src/endpoints/events/onWorkoutDocumentUpdate.ts", "hash": "16140643962099018580"}, {"file": "functions/src/endpoints/index.ts", "hash": "2065552665966803360"}, {"file": "functions/src/endpoints/requests/appUser/adminUpdateUser.ts", "hash": "12659587842130533413"}, {"file": "functions/src/endpoints/requests/appUser/deleteAppUserById.ts", "hash": "5226223118533730423"}, {"file": "functions/src/endpoints/requests/appUser/getAppUserById.ts", "hash": "2769600203184841060"}, {"file": "functions/src/endpoints/requests/appUser/getAppUserCollectionSummary.ts", "hash": "12591116154036021226"}, {"file": "functions/src/endpoints/requests/appUser/getGetGapsInHealthStats.ts", "hash": "9781352762995256283", "deps": ["npm:date-fns-tz"]}, {"file": "functions/src/endpoints/requests/appUser/importFitbitDataForUser.ts", "hash": "392371878741120243"}, {"file": "functions/src/endpoints/requests/appUser/importUserHealthStats.ts", "hash": "18382117701892316041"}, {"file": "functions/src/endpoints/requests/appUser/importUsers.ts", "hash": "11699099504754537744"}, {"file": "functions/src/endpoints/requests/appUser/index.ts", "hash": "13038027323785864645"}, {"file": "functions/src/endpoints/requests/appUser/indexAlgoliaBaseAppUsers.ts", "hash": "10539949444718174138"}, {"file": "functions/src/endpoints/requests/appUser/mutateHealthData.ts", "hash": "5729178926293313270"}, {"file": "functions/src/endpoints/requests/appUser/quizStreakStartAppUsers.ts", "hash": "7194944582261081813"}, {"file": "functions/src/endpoints/requests/appUser/searchAppUser.ts", "hash": "17413367085002923210"}, {"file": "functions/src/endpoints/requests/appUser/updateAppUserDocument.ts", "hash": "5416859771321825928"}, {"file": "functions/src/endpoints/requests/auth/authRedirect.ts", "hash": "15083927779302401925"}, {"file": "functions/src/endpoints/requests/auth/emailTrackingDeviceConnection.ts", "hash": "556527386615777731"}, {"file": "functions/src/endpoints/requests/auth/getAuthUser.ts", "hash": "17919007564687482563"}, {"file": "functions/src/endpoints/requests/auth/index.ts", "hash": "8346302145156250567"}, {"file": "functions/src/endpoints/requests/auth/isValidNewUserCredentials.ts", "hash": "17490509866952578353"}, {"file": "functions/src/endpoints/requests/auth/logout.ts", "hash": "14932973902240593868"}, {"file": "functions/src/endpoints/requests/auth/sendLoginEmail.ts", "hash": "9761851305800632281"}, {"file": "functions/src/endpoints/requests/auth/sendWelcomeEmail.ts", "hash": "3909776182541908848"}, {"file": "functions/src/endpoints/requests/auth/sendWelcomeEmailBulk.ts", "hash": "15040949564908762514"}, {"file": "functions/src/endpoints/requests/auth/signUpAppUser.ts", "hash": "3035567012593130701"}, {"file": "functions/src/endpoints/requests/auth/signUpOperation.ts", "hash": "15618996594769562677"}, {"file": "functions/src/endpoints/requests/auth/verifyAuthToken.ts", "hash": "9492068722898762816"}, {"file": "functions/src/endpoints/requests/challenge/challengeOperationRequest.ts", "hash": "17371142962426290923"}, {"file": "functions/src/endpoints/requests/challenge/getChallengeById.ts", "hash": "1635239981497224125"}, {"file": "functions/src/endpoints/requests/challenge/getChallengeEmails.ts", "hash": "9056421216933493117"}, {"file": "functions/src/endpoints/requests/challenge/index.ts", "hash": "13838647178764627844"}, {"file": "functions/src/endpoints/requests/challenge/leaveChallengeRequest.ts", "hash": "16849035825258424681"}, {"file": "functions/src/endpoints/requests/challenge/sendChallengeInviteNotifications.ts", "hash": "18088684170351367944"}, {"file": "functions/src/endpoints/requests/challenge/updateChallengeDocument.ts", "hash": "17888011333067751222"}, {"file": "functions/src/endpoints/requests/getFlyFitAppSummary.ts", "hash": "6733741805707645840"}, {"file": "functions/src/endpoints/requests/index.ts", "hash": "3150698471667908366"}, {"file": "functions/src/endpoints/requests/organization/getOrganizationById.ts", "hash": "10180333029140784285"}, {"file": "functions/src/endpoints/requests/organization/index.ts", "hash": "2069170087762702804"}, {"file": "functions/src/endpoints/requests/playground/checkIn.playground.ts", "hash": "11230364866979304427"}, {"file": "functions/src/endpoints/requests/playground/createDocument.playground.ts", "hash": "3069176213400357135"}, {"file": "functions/src/endpoints/requests/playground/email.playground.ts", "hash": "295347549996530714"}, {"file": "functions/src/endpoints/requests/playground/index.ts", "hash": "3147629177279299413"}, {"file": "functions/src/endpoints/requests/reports/index.ts", "hash": "16502749119387698457"}, {"file": "functions/src/endpoints/requests/reports/recreateReport.ts", "hash": "14060043320465192903"}, {"file": "functions/src/endpoints/requests/reports/signedReportUrl.ts", "hash": "13543421555903334951"}, {"file": "functions/src/endpoints/requests/trainer/clientSummary2.ts", "hash": "456193615279223194"}, {"file": "functions/src/endpoints/requests/trainer/index.ts", "hash": "16078859585340712232"}, {"file": "functions/src/endpoints/requests/trainer/trainerClientSummary2.ts", "hash": "14609992282948940854"}, {"file": "functions/src/endpoints/requests/web/index.ts", "hash": "9234185477164556455"}, {"file": "functions/src/endpoints/requests/web/optimizedLinking.ts", "hash": "929117397594797672"}, {"file": "functions/src/endpoints/requests/wellnessEducation/getOgImageUrl.ts", "hash": "10328404935450831689"}, {"file": "functions/src/endpoints/requests/wellnessEducation/index.ts", "hash": "13509137247382555129"}, {"file": "functions/src/endpoints/requests/wellnessEducation/wellnessBlog.ts", "hash": "1958574953055056474"}, {"file": "functions/src/endpoints/requests/wellnessEducation/wellnessQuiz.ts", "hash": "14459499985200482050"}, {"file": "functions/src/endpoints/requests/wellnessEducation/wellnessQuote.ts", "hash": "15062362092937677605"}, {"file": "functions/src/endpoints/scheduled/index.ts", "hash": "16325863000038396431"}, {"file": "functions/src/endpoints/scheduled/onChallengeCheckInNotification.ts", "hash": "7816727116665838968"}, {"file": "functions/src/endpoints/scheduled/onChallengeEnded.ts", "hash": "2323504871603169135"}, {"file": "functions/src/endpoints/scheduled/onChallengeGroupBatchScan.ts", "hash": "17387214147163871969"}, {"file": "functions/src/endpoints/scheduled/onStreakNotificationTesting.ts", "hash": "8735199205170681437"}, {"file": "functions/src/endpoints/taskFunctions/handleChallengeGroupAggregation.ts", "hash": "16164419210780611509"}, {"file": "functions/src/endpoints/taskFunctions/handleCreateSummaryReport.ts", "hash": "7986115555186171073"}, {"file": "functions/src/endpoints/taskFunctions/handleSendEmail.ts", "hash": "7697595079357187444"}, {"file": "functions/src/endpoints/taskFunctions/index.ts", "hash": "8696964842074354977"}, {"file": "functions/src/endpoints/transformations/deleteAppUsersByIds.ts", "hash": "12372003035107966762"}, {"file": "functions/src/endpoints/transformations/deleteEmptyDocsInCollection.ts", "hash": "1961955289493414990"}, {"file": "functions/src/endpoints/transformations/getChallengeDataExport.ts", "hash": "9330705584923358624"}, {"file": "functions/src/endpoints/transformations/getDuplicateAppUsers.ts", "hash": "1788777312888547055"}, {"file": "functions/src/endpoints/transformations/getDuplicateAppUsersByName.ts", "hash": "10565197122044207175"}, {"file": "functions/src/endpoints/transformations/getDuplicateExpoPushTokenUsers.ts", "hash": "3824865460877643314"}, {"file": "functions/src/endpoints/transformations/index.ts", "hash": "9317120285691132047"}, {"file": "functions/src/endpoints/transformations/removePropertyFromAppUsers.ts", "hash": "11938571728865285972", "deps": ["npm:firebase-admin"]}, {"file": "functions/src/endpoints/transformations/transformAppUserDeviceTrackerField.ts", "hash": "1419008710282762686"}, {"file": "functions/src/endpoints/transformations/transformAppUserGoals.ts", "hash": "17981334356107043982"}, {"file": "functions/src/endpoints/transformations/transformAppUserLogs.ts", "hash": "14027846155564323075"}, {"file": "functions/src/endpoints/transformations/transformAppUserNotificationSettings.ts", "hash": "6019764715791787744"}, {"file": "functions/src/endpoints/transformations/transformAppUserToOrg.ts", "hash": "2957599025506024777"}, {"file": "functions/src/endpoints/transformations/transformAppUsersPhoneNumber.ts", "hash": "14786716016335528973"}, {"file": "functions/src/endpoints/transformations/transformAppUsersToHealthData.ts", "hash": "3637848679675862690"}, {"file": "functions/src/endpoints/transformations/transformChallengeAggregationConfig.ts", "hash": "5669575493493511244"}, {"file": "functions/src/endpoints/transformations/transformChallengeUsersAccepted.ts", "hash": "15299652622507642496"}, {"file": "functions/src/endpoints/transformations/transformMovementStreaks.ts", "hash": "11334513785209138633"}, {"file": "functions/src/endpoints/transformations/transformQuizStreaks.ts", "hash": "2735946435065875461"}, {"file": "functions/src/endpoints/transformations/transformTest.ts", "hash": "16979775885584396735"}, {"file": "functions/src/express.d.ts", "hash": "3120498156286885480", "deps": ["npm:express"]}, {"file": "functions/src/firebase.ts", "hash": "496904142440766847"}, {"file": "functions/src/firestore.ts", "hash": "9571781144197613141", "deps": ["npm:firebase-admin"]}, {"file": "functions/src/firestoreHelpers.ts", "hash": "12715655237832686739"}, {"file": "functions/src/index.ts", "hash": "13248293694186704652"}, {"file": "functions/src/types/challengeReportDataExportTypes.ts", "hash": "14388989337116165702"}, {"file": "functions/src/types/emailTypes.ts", "hash": "7885160936608818319"}, {"file": "functions/src/types/firebaseTypes.ts", "hash": "9444685619547126142", "deps": ["npm:firebase-admin"]}, {"file": "functions/src/types/index.ts", "hash": "11760251537150394308"}, {"file": "functions/src/types/reportGenerationTypes.ts", "hash": "9366096190345995334"}, {"file": "functions/src/types/sevenDayReportDataExportTypes.ts", "hash": "10280059352225452127"}, {"file": "functions/src/types/summaryTypes.ts", "hash": "7051185436684964544"}, {"file": "functions/src/types/taskQueueTypes.ts", "hash": "105105500505506004"}, {"file": "functions/src/utils/domain/algoliaHelpers.ts", "hash": "3611329172411739825"}, {"file": "functions/src/utils/domain/appUserChallenges.ts", "hash": "7688300361395600560", "deps": ["npm:firebase-admin"]}, {"file": "functions/src/utils/domain/appUserHelpers.ts", "hash": "13671726753761661056"}, {"file": "functions/src/utils/domain/appUserTransformer.ts", "hash": "10900829057458059522"}, {"file": "functions/src/utils/domain/authHelpers.ts", "hash": "4047610508074274575"}, {"file": "functions/src/utils/domain/challengeHelpers.test-exclude.ts", "hash": "5548327884488148832"}, {"file": "functions/src/utils/domain/challengeHelpers.ts", "hash": "18124629775887705111", "deps": ["npm:date-fns"]}, {"file": "functions/src/utils/domain/challengeReportDataExport.ts", "hash": "7220226600758043819"}, {"file": "functions/src/utils/domain/challengeTransformer.ts", "hash": "8089602936428107059"}, {"file": "functions/src/utils/domain/collectionSummaryHelpers.ts", "hash": "6492934993069364867"}, {"file": "functions/src/utils/domain/empty.ts", "hash": "10168387770860506704"}, {"file": "functions/src/utils/domain/googleSheetsHelpers.ts", "hash": "2659427262793019663"}, {"file": "functions/src/utils/domain/healthDataHelpers.ts", "hash": "7632519795411077536"}, {"file": "functions/src/utils/domain/healthStatsHelpers.test-exclude.ts", "hash": "14802029196229308573"}, {"file": "functions/src/utils/domain/healthStatsHelpers.ts", "hash": "17445424359627084695", "deps": ["npm:date-fns"]}, {"file": "functions/src/utils/domain/index.ts", "hash": "1579523574083674090"}, {"file": "functions/src/utils/domain/notificationContent.ts", "hash": "4567889243399885893", "deps": ["npm:date-fns-tz"]}, {"file": "functions/src/utils/domain/quizStreak.ts", "hash": "14134904403009093007"}, {"file": "functions/src/utils/domain/reportGeneration.ts", "hash": "7501395266792278917", "deps": ["report-generation"]}, {"file": "functions/src/utils/domain/scheduledHelpers.ts", "hash": "3120486543233245862", "deps": ["npm:date-fns-tz"]}, {"file": "functions/src/utils/domain/sendNotification.ts", "hash": "13273208029041638014"}, {"file": "functions/src/utils/domain/sevenDayReportDataExport.ts", "hash": "4343751645477098524", "deps": ["npm:date-fns"]}, {"file": "functions/src/utils/domain/streakHelpers.test-exclude.ts", "hash": "2612156553514548245"}, {"file": "functions/src/utils/domain/streakHelpersDates.ts", "hash": "2507186047482093785"}, {"file": "functions/src/utils/domain/streakHelpersMovement.ts", "hash": "8771968606436348052"}, {"file": "functions/src/utils/domain/workoutHelpers.ts", "hash": "5520675837874190923"}, {"file": "functions/src/utils/index.ts", "hash": "6765222279894460959"}, {"file": "functions/src/utils/primitives/arrayHelpers.ts", "hash": "1969936953202391828"}, {"file": "functions/src/utils/primitives/conversionHelpers.ts", "hash": "2007463096808283706"}, {"file": "functions/src/utils/primitives/dateHelpers.test.ts", "hash": "17751916393156242904"}, {"file": "functions/src/utils/primitives/dateHelpers.ts", "hash": "14362513243530568768", "deps": ["npm:date-fns"]}, {"file": "functions/src/utils/primitives/dateHelpersPerf.test.ts", "hash": "12401370446896214297"}, {"file": "functions/src/utils/primitives/fileHelpers.ts", "hash": "14176368706903840602"}, {"file": "functions/src/utils/primitives/index.ts", "hash": "12408051274815610577"}, {"file": "functions/src/utils/primitives/isoDateHelpers.ts", "hash": "3930068114796413724"}, {"file": "functions/src/utils/primitives/loggingHelpers.ts", "hash": "17125480031403800210"}, {"file": "functions/src/utils/primitives/mathHelpers.ts", "hash": "2872416412363191246"}, {"file": "functions/src/utils/primitives/performanceHelpers.ts", "hash": "14455994777219212752"}, {"file": "functions/src/utils/primitives/primitiveTransformers.ts", "hash": "10459910046066519249", "deps": ["npm:firebase-admin"]}, {"file": "functions/src/utils/primitives/promiseHelpers.test.ts", "hash": "5336385905142367192"}, {"file": "functions/src/utils/primitives/promiseHelpers.ts", "hash": "7956330659833839679", "deps": ["npm:p-limit", "npm:async-retry"]}, {"file": "functions/src/utils/primitives/timingHelpers.ts", "hash": "451249305810201263"}, {"file": "functions/src/utils/primitives/utils.ts", "hash": "13679122760651166335", "deps": ["npm:dequal"]}, {"file": "functions/src/utils/primitives/uuid.ts", "hash": "13168020652069847338", "deps": ["npm:uuid"]}, {"file": "functions/src/utils/primitives/validation.ts", "hash": "13140136968871167924"}, {"file": "functions/src/utils/setup/cjs-shim.ts", "hash": "12183125106293231805"}, {"file": "functions/src/utils/setup/setupGlobalEnvironment.ts", "hash": "16267394051555104640", "deps": ["npm:dotenv", "npm:firebase-admin", "npm:@google-cloud/storage"]}, {"file": "functions/src/utils/utils-firebase/cacheHelpers.ts", "hash": "6345907767012337295"}, {"file": "functions/src/utils/utils-firebase/cloudHelpers.ts", "hash": "9742754665695236631"}, {"file": "functions/src/utils/utils-firebase/cors.ts", "hash": "2618946460651942540"}, {"file": "functions/src/utils/utils-firebase/databaseHelpers.ts", "hash": "4091038232493795494"}, {"file": "functions/src/utils/utils-firebase/errorHandlingHelpers.ts", "hash": "13521522262351391386"}, {"file": "functions/src/utils/utils-firebase/firebaseHelpers.ts", "hash": "11490100616381093760", "deps": ["npm:firebase-admin"]}, {"file": "functions/src/utils/utils-firebase/firebaseRequestFactories.ts", "hash": "11485021739135820287", "deps": ["npm:express"]}, {"file": "functions/src/utils/utils-firebase/index.ts", "hash": "11980779855890853821"}, {"file": "functions/src/utils/utils-firebase/processingHelpers.ts", "hash": "4047321679312182610"}, {"file": "functions/src/utils/utils-firebase/queueHelpers.ts", "hash": "11290473178131919278", "deps": ["npm:firebase-admin", "npm:google-auth-library"]}, {"file": "functions/swagger-docs/.npmrc", "hash": "4925775595674489185"}, {"file": "functions/swagger-docs/openapi3_1.yml", "hash": "1565427619390841708"}, {"file": "functions/swagger-docs/package-lock.json", "hash": "10754418930180209523"}, {"file": "functions/swagger-docs/package.json", "hash": "17283059152519532110"}, {"file": "functions/tsconfig.json", "hash": "8836980701487131041"}], "fly-fit": [{"file": ".augment-guidelines", "hash": "13259891539328855501"}, {"file": ".easignore", "hash": "1615952987315798592"}, {"file": ".env.vault", "hash": "6718115741166700977"}, {"file": ".firebaserc", "hash": "12933771097652935547"}, {"file": ".gcloudignore", "hash": "12554333283433059213"}, {"file": ".gemini/config.yaml", "hash": "395645197931318382"}, {"file": ".gemini/styleguide.md", "hash": "3945290567507495984"}, {"file": ".gitattributes", "hash": "17264509817068060521"}, {"file": ".github/workflows/app-ci-cd.yml", "hash": "10580158364086070907"}, {"file": ".github/workflows/main.yml", "hash": "14758984984286746896"}, {"file": ".giti<PERSON>re", "hash": "2412945597243007935"}, {"file": ".npmrc", "hash": "13394946703242683089"}, {"file": ".releaserc.json", "hash": "2396600120203540290"}, {"file": ".vscode/extensions.json", "hash": "6552482116567704354"}, {"file": ".vscode/launch.json", "hash": "1790628154810723434"}, {"file": ".vscode/settings.json", "hash": "1125781483484100057"}, {"file": ".vscode/snippets.code-snippets", "hash": "8722961001723699834"}, {"file": ".watchmanconfig", "hash": "13871716915581259318"}, {"file": "Brewfile", "hash": "466385350353108361"}, {"file": "Brewfile.lock.json", "hash": "6866012834611562731"}, {"file": "CHANGELOG.md", "hash": "3801820947769914300"}, {"file": "GoogleService-Info.dev.plist", "hash": "7176956709901590329"}, {"file": "GoogleService-Info.plist", "hash": "2737191044778112312"}, {"file": "README.md", "hash": "2848626543924332367"}, {"file": "__mocks__/data/rawHealthStats.json", "hash": "17411882938032549965"}, {"file": "app.config.cjs", "hash": "9863540606923214758", "deps": ["npm:ts-node"]}, {"file": "app.config.ts", "hash": "8439898988941578971", "deps": ["npm:expo"]}, {"file": "assets/adaptive-icon.png", "hash": "17469530240401415090"}, {"file": "assets/android-icon-96x96.png", "hash": "13642696376505330959"}, {"file": "assets/apple_health_allow_permissions.png", "hash": "1991530200529169654"}, {"file": "assets/apple_health_icon.png", "hash": "17497961900152671215"}, {"file": "assets/default_profile.png", "hash": "1730679942937417301"}, {"file": "assets/default_team.png", "hash": "2427511113314417073"}, {"file": "assets/example_movement_streak_ui.png", "hash": "2177378145889060675"}, {"file": "assets/f-black-none.svg", "hash": "3963930144074703884"}, {"file": "assets/f-black-white-header.webp", "hash": "10240001360237935211"}, {"file": "assets/f-black-white.svg", "hash": "14705342672606975366"}, {"file": "assets/f-white-none.svg", "hash": "4267618617122497919"}, {"file": "assets/f-wite-black.svg", "hash": "1240219153810284463"}, {"file": "assets/f_black_none.png", "hash": "502240141882293269"}, {"file": "assets/favicon.png", "hash": "3246433460927641039"}, {"file": "assets/fitbit_logo.png", "hash": "4996276905525447627"}, {"file": "assets/food_cover.png", "hash": "2616041783852046590"}, {"file": "assets/google-logo.png", "hash": "11074318101148086256"}, {"file": "assets/google_health_connect_icon.png", "hash": "16527705896182203290"}, {"file": "assets/icon-512.png", "hash": "18035235988123403906"}, {"file": "assets/icon.png", "hash": "18343954751023510333"}, {"file": "assets/mileage_challenge_cover_photo.png", "hash": "5925705145404782997"}, {"file": "assets/movement_streak_example.png", "hash": "3476555030702294364"}, {"file": "assets/placeholder_workout_image.png", "hash": "1592330623850421231"}, {"file": "assets/screenshots/Fly Fit v1.mockup", "hash": "2715798034223958189"}, {"file": "assets/screenshots/Fly Fit v2 Android.mockup", "hash": "1718167828404443074"}, {"file": "assets/screenshots/FlyFit v2 iPad.mockup", "hash": "8946926418152558960"}, {"file": "assets/screenshots/FlyFit v2 iPhone.mockup", "hash": "7697799629555981880"}, {"file": "assets/screenshots/android-/archive/May 2024/flyfit-challenge-leaderboard.png", "hash": "12214475493747059880"}, {"file": "assets/screenshots/android-/archive/May 2024/flyfit-challenge.png", "hash": "16146832379334605221"}, {"file": "assets/screenshots/android-/archive/May 2024/flyfit-challenges.png", "hash": "12586624320783564700"}, {"file": "assets/screenshots/android-/archive/May 2024/flyfit-homepage.png", "hash": "17528598815660220485"}, {"file": "assets/screenshots/android-/archive/May 2024/flyfit-notification-settings.png", "hash": "996992836727009154"}, {"file": "assets/screenshots/android-/archive/May 2024/flyfit-workout.png", "hash": "11127286356407577477"}, {"file": "assets/screenshots/android-/archive/October 2024/flyfit-challenge.png", "hash": "16688700989937793800"}, {"file": "assets/screenshots/android-/archive/October 2024/flyfit-health-sync-settings.png", "hash": "8485544511097172879"}, {"file": "assets/screenshots/android-/archive/October 2024/flyfit-homepage-day.png", "hash": "5468442338130195157"}, {"file": "assets/screenshots/android-/archive/October 2024/flyfit-homepage-week.png", "hash": "10440390626162732057"}, {"file": "assets/screenshots/android-/archive/October 2024/flyfit-wellness-education.png", "hash": "288602374205318957"}, {"file": "assets/screenshots/android-/archive/October 2024/flyfit-workouts.png", "hash": "13512527330287353983"}, {"file": "assets/screenshots/android-/flyfit-challenge-page.png", "hash": "7378960891665308497"}, {"file": "assets/screenshots/android-/flyfit-health-sync-settings-fitbit.png", "hash": "17545586399955871811"}, {"file": "assets/screenshots/android-/flyfit-home-page.png", "hash": "5024462720575277173"}, {"file": "assets/screenshots/android-/flyfit-leaderboard.png", "hash": "1964944631130867714"}, {"file": "assets/screenshots/android-/flyfit-week-view.png", "hash": "11031721085872862365"}, {"file": "assets/screenshots/android-/flyfit-wellness-education.png", "hash": "12707691916725405054"}, {"file": "assets/screenshots/android-/flyfit-workout.png", "hash": "8765185229092765971"}, {"file": "assets/screenshots/ios/iPad/archive/August 2024/flyfit-challenge-2048x2732.png", "hash": "133343038246350294"}, {"file": "assets/screenshots/ios/iPad/archive/August 2024/flyfit-challenges-2048x2732.png", "hash": "11246381588002975383"}, {"file": "assets/screenshots/ios/iPad/archive/August 2024/flyfit-feed-2048x2732.png", "hash": "4157880614424214357"}, {"file": "assets/screenshots/ios/iPad/archive/August 2024/flyfit-homepage-day-2048x2732.png", "hash": "14074998573517445387"}, {"file": "assets/screenshots/ios/iPad/archive/August 2024/flyfit-homepage-month-2048x2732.png", "hash": "5453701209969030840"}, {"file": "assets/screenshots/ios/iPad/archive/August 2024/flyfit-homepage-week-2048x2732.png", "hash": "11180168047729569395"}, {"file": "assets/screenshots/ios/iPad/archive/August 2024/flyfit-notification-settings-2048x2732.png", "hash": "10446327135131536139"}, {"file": "assets/screenshots/ios/iPad/archive/August 2024/flyfit-weight-2048x2732.png", "hash": "9652097025659788684"}, {"file": "assets/screenshots/ios/iPad/archive/August 2024/flyfit-workout-2048x2732.png", "hash": "12810619789192546866"}, {"file": "assets/screenshots/ios/iPad/archive/June 2024/flyfit-challenge-2048x2732.png", "hash": "9990433083195656164"}, {"file": "assets/screenshots/ios/iPad/archive/June 2024/flyfit-challenges-2048x2732.png", "hash": "11998265634160585202"}, {"file": "assets/screenshots/ios/iPad/archive/June 2024/flyfit-homepage-2048x2732 copy.png", "hash": "15561780224529763298"}, {"file": "assets/screenshots/ios/iPad/archive/June 2024/flyfit-homepage-2048x2732.png", "hash": "1953241021380029852"}, {"file": "assets/screenshots/ios/iPad/archive/June 2024/flyfit-login-2048x2732.png", "hash": "3887918536586428902"}, {"file": "assets/screenshots/ios/iPad/archive/June 2024/flyfit-workout-2048x2732 copy.png", "hash": "1215380775287210536"}, {"file": "assets/screenshots/ios/iPad/archive/June 2024/flyfit-workout-2048x2732.png", "hash": "14722483823813411942"}, {"file": "assets/screenshots/ios/iPad/archive/November 2024/flyfit-challenge-2064x2752.png", "hash": "13803957683812357435"}, {"file": "assets/screenshots/ios/iPad/archive/November 2024/flyfit-health-sync-settings-2064x2752.png", "hash": "13100114472852866858"}, {"file": "assets/screenshots/ios/iPad/archive/November 2024/flyfit-homepage-day-2064x2752.png", "hash": "15391214998246898973"}, {"file": "assets/screenshots/ios/iPad/archive/November 2024/flyfit-homepage-week-2064x2752.png", "hash": "13444285780871349482"}, {"file": "assets/screenshots/ios/iPad/archive/November 2024/flyfit-wellness-education-2064x2752.png", "hash": "565382986075037654"}, {"file": "assets/screenshots/ios/iPad/archive/November 2024/flyfit-workout-2064x2752.png", "hash": "2798946951140385531"}, {"file": "assets/screenshots/ios/iPad/flyfit-challenge.png", "hash": "2465030657231907329"}, {"file": "assets/screenshots/ios/iPad/flyfit-health-sync-settings.png", "hash": "5837673631051944349"}, {"file": "assets/screenshots/ios/iPad/flyfit-home-page.png", "hash": "7155813343787486492"}, {"file": "assets/screenshots/ios/iPad/flyfit-week-view.png", "hash": "15939690766480837588"}, {"file": "assets/screenshots/ios/iPad/flyfit-wellness-education.png", "hash": "13810282840502075029"}, {"file": "assets/screenshots/ios/iPad/flyfit-workout.png", "hash": "3432611729806045812"}, {"file": "assets/screenshots/ios/iPhone/archive/August 2024/flyfit-challenge-bottom-1242x2208.png", "hash": "7963403100199466872"}, {"file": "assets/screenshots/ios/iPhone/archive/August 2024/flyfit-challenge-top-1242x2208.png", "hash": "17810010313901935510"}, {"file": "assets/screenshots/ios/iPhone/archive/August 2024/flyfit-challenges-1242x2208.png", "hash": "2605108826644595316"}, {"file": "assets/screenshots/ios/iPhone/archive/August 2024/flyfit-feed-1242x2208.png", "hash": "10261014224469882437"}, {"file": "assets/screenshots/ios/iPhone/archive/August 2024/flyfit-homepage-day-1242x2208.png", "hash": "8010393096144870407"}, {"file": "assets/screenshots/ios/iPhone/archive/August 2024/flyfit-homepage-month-1242x2208.png", "hash": "1422930443080311470"}, {"file": "assets/screenshots/ios/iPhone/archive/August 2024/flyfit-homepage-week-1242x2208.png", "hash": "9673426083121842103"}, {"file": "assets/screenshots/ios/iPhone/archive/August 2024/flyfit-notification-settings-1242x2208.png", "hash": "13199618123564768705"}, {"file": "assets/screenshots/ios/iPhone/archive/August 2024/flyfit-weight-1242x2208.png", "hash": "9461521092273380813"}, {"file": "assets/screenshots/ios/iPhone/archive/August 2024/flyfit-workout-1242x2208.png", "hash": "13995347098696039178"}, {"file": "assets/screenshots/ios/iPhone/archive/December 2023/flyfit-challenge-1242x2208.png", "hash": "3913998521924642690"}, {"file": "assets/screenshots/ios/iPhone/archive/December 2023/flyfit-challenge-bottom-1242x2208.png", "hash": "2125502454273246214"}, {"file": "assets/screenshots/ios/iPhone/archive/December 2023/flyfit-challenge-top-1242x2208.png", "hash": "10957719913180133772"}, {"file": "assets/screenshots/ios/iPhone/archive/December 2023/flyfit-challenges-1242x2208 copy.png", "hash": "16019153746702323564"}, {"file": "assets/screenshots/ios/iPhone/archive/December 2023/flyfit-challenges-1242x2208.png", "hash": "7628838557511657110"}, {"file": "assets/screenshots/ios/iPhone/archive/December 2023/flyfit-goal-1242x2208.png", "hash": "6044868349296519879"}, {"file": "assets/screenshots/ios/iPhone/archive/December 2023/flyfit-homepage-1242x2208.png", "hash": "17576384894380986214"}, {"file": "assets/screenshots/ios/iPhone/archive/December 2023/flyfit-homepage-refreshed-1242x2208.png", "hash": "16616713041674037705"}, {"file": "assets/screenshots/ios/iPhone/archive/December 2023/flyfit-homepage-streaks-1242x2208 copy.png", "hash": "4130699651087389233"}, {"file": "assets/screenshots/ios/iPhone/archive/December 2023/flyfit-homepage-streaks-1242x2208.png", "hash": "3875055562941416838"}, {"file": "assets/screenshots/ios/iPhone/archive/December 2023/flyfit-homepage-welless-summary-1242x2208 copy.png", "hash": "761742086841253429"}, {"file": "assets/screenshots/ios/iPhone/archive/December 2023/flyfit-homepage-welless-summary-1242x2208.png", "hash": "406610188446780263"}, {"file": "assets/screenshots/ios/iPhone/archive/December 2023/flyfit-login-1242x2208.png", "hash": "9264775829324791992"}, {"file": "assets/screenshots/ios/iPhone/archive/December 2023/flyfit-notification-settings-1242x2208.png", "hash": "8744708870690150752"}, {"file": "assets/screenshots/ios/iPhone/archive/December 2023/flyfit-workout-1242x2208 copy.png", "hash": "4449152892350322380"}, {"file": "assets/screenshots/ios/iPhone/archive/December 2023/flyfit-workout-1242x2208.png", "hash": "3861493986983946100"}, {"file": "assets/screenshots/ios/iPhone/archive/June 2023/flyfit-challenges-1284×2778.png", "hash": "3537927271709877478"}, {"file": "assets/screenshots/ios/iPhone/archive/June 2023/flyfit-homepage-streaks-1284×2778.png", "hash": "18335327747086771099"}, {"file": "assets/screenshots/ios/iPhone/archive/June 2023/flyfit-homepage-wellness-summary-1284×2778.png", "hash": "3156443422571591905"}, {"file": "assets/screenshots/ios/iPhone/archive/June 2024/flyfit-challenge-bottom-1284×2778.png", "hash": "11441706525844264613"}, {"file": "assets/screenshots/ios/iPhone/archive/June 2024/flyfit-challenge-top-1284×2778.png", "hash": "13218274470033917357"}, {"file": "assets/screenshots/ios/iPhone/archive/June 2024/flyfit-challenges-1284x2778.png", "hash": "15817307063631938682"}, {"file": "assets/screenshots/ios/iPhone/archive/June 2024/flyfit-feed-1284x2778.png", "hash": "639240823578083192"}, {"file": "assets/screenshots/ios/iPhone/archive/June 2024/flyfit-health-sync-settings-1284x2778.png", "hash": "11978532406680510224"}, {"file": "assets/screenshots/ios/iPhone/archive/June 2024/flyfit-homepage-day-1284x2778.png", "hash": "16544388489724996037"}, {"file": "assets/screenshots/ios/iPhone/archive/June 2024/flyfit-homepage-month-1284x2778.png", "hash": "6473605525890342972"}, {"file": "assets/screenshots/ios/iPhone/archive/June 2024/flyfit-homepage-week-1284x2778.png", "hash": "11053823666915341136"}, {"file": "assets/screenshots/ios/iPhone/archive/June 2024/flyfit-notification-settings-1284×2778.png", "hash": "9699460363278506628"}, {"file": "assets/screenshots/ios/iPhone/archive/June 2024/flyfit-weight-1284x2778.png", "hash": "5599108765007383313"}, {"file": "assets/screenshots/ios/iPhone/archive/June 2024/flyfit-wellness-education-1284x2778.png", "hash": "3008605174355641243"}, {"file": "assets/screenshots/ios/iPhone/archive/June 2024/flyfit-workout-1284×2778.png", "hash": "17510819306415385994"}, {"file": "assets/screenshots/ios/iPhone/flyfit-challenge-leaderboard.png", "hash": "15575437991386977752"}, {"file": "assets/screenshots/ios/iPhone/flyfit-challenge-page.png", "hash": "10609878989771864107"}, {"file": "assets/screenshots/ios/iPhone/flyfit-health-sync-settings-apple.png", "hash": "7097176185242548140"}, {"file": "assets/screenshots/ios/iPhone/flyfit-home-page.png", "hash": "146993862658402493"}, {"file": "assets/screenshots/ios/iPhone/flyfit-team-feed.JPEG", "hash": "11799960990099050170"}, {"file": "assets/screenshots/ios/iPhone/flyfit-week-view.png", "hash": "2776524083302950101"}, {"file": "assets/screenshots/ios/iPhone/flyfit-wellness-education.png", "hash": "17542613676699777249"}, {"file": "assets/screenshots/ios/iPhone/flyfit-workout.png", "hash": "11201962567516955645"}, {"file": "assets/splash_black_fly_guy.png", "hash": "15845985366345116846"}, {"file": "assets/splash_white_fly_guy.png", "hash": "5745315215646087764"}, {"file": "assets/step_challenge_cover_photo.png", "hash": "14249074943360797042"}, {"file": "assets/svgs/Fly Fit Icons_Avg Mleage.svg", "hash": "1213442313706722750"}, {"file": "assets/svgs/Fly Fit Icons_Calendar.svg", "hash": "2603033034320060484"}, {"file": "assets/svgs/Fly Fit Icons_Challenges.svg", "hash": "3601689800831152133"}, {"file": "assets/svgs/Fly Fit Icons_Clipboard.svg", "hash": "6813584433481129452"}, {"file": "assets/svgs/Fly Fit Icons_Cycling.svg", "hash": "4343166163509325665"}, {"file": "assets/svgs/Fly Fit Icons_Duration.svg", "hash": "3301544545222349187"}, {"file": "assets/svgs/Fly Fit Icons_HIIT.svg", "hash": "4084404191008090030"}, {"file": "assets/svgs/Fly Fit Icons_Health Score.svg", "hash": "15711536467835931640"}, {"file": "assets/svgs/Fly Fit Icons_Man Running.svg", "hash": "12641217010892873953"}, {"file": "assets/svgs/Fly Fit Icons_Max.svg", "hash": "18305128620572045555"}, {"file": "assets/svgs/Fly Fit Icons_Meal.svg", "hash": "18206572727654337541"}, {"file": "assets/svgs/Fly Fit Icons_Member Icon.svg", "hash": "10875498690711610850"}, {"file": "assets/svgs/Fly Fit Icons_Rating Position.svg", "hash": "374496810631591791"}, {"file": "assets/svgs/Fly Fit Icons_Run.svg", "hash": "6147305357723096871"}, {"file": "assets/svgs/Fly Fit Icons_Sport.svg", "hash": "10988649264621285388"}, {"file": "assets/svgs/Fly Fit Icons_Streak.svg", "hash": "6259423620913938807"}, {"file": "assets/svgs/Fly Fit Icons_Swim.svg", "hash": "1567014406720468318"}, {"file": "assets/svgs/Fly Fit Icons_Total Mileage.svg", "hash": "11045974426565478422"}, {"file": "assets/svgs/Fly Fit Icons_Walk.svg", "hash": "9206503599046620043"}, {"file": "assets/svgs/Fly Fit Icons_Weight.svg", "hash": "2741631846368951258"}, {"file": "assets/svgs/Fly Fit Icons_Weights.svg", "hash": "15322786071551286613"}, {"file": "assets/svgs/Fly Fit Icons_Workout.svg", "hash": "14190602197451095580"}, {"file": "assets/svgs/Fly Fit Icons_Yoga.svg", "hash": "17951195670222470908"}, {"file": "assets/svgs/group-svgrepo-com.svg", "hash": "4505650673759673768"}, {"file": "assets/svgs/person-male-svgrepo-com.svg", "hash": "6623319749586964609"}, {"file": "assets/website/apple_watch_ios_icon.png", "hash": "1326413501508692064"}, {"file": "assets/website/businesses/cityx.png", "hash": "3659947632618451533"}, {"file": "assets/website/businesses/montage.png", "hash": "18435173661433502157"}, {"file": "assets/website/businesses/puc national.png", "hash": "8929465915536019395"}, {"file": "assets/website/businesses/vpan tan.png", "hash": "13411788543359744613"}, {"file": "assets/website/businesses/vpan.png", "hash": "16876387521448602769"}, {"file": "assets/website/fitbit_ios_icon.png", "hash": "8365977958153694080"}, {"file": "assets/website/fly-fit-calendar-day-view-phone.png", "hash": "16055152419385329841"}, {"file": "assets/website/fly-fit-calendar-meals-phone.png", "hash": "10200510179039266937"}, {"file": "assets/website/fly-fit-health-sync-settings-phone.png", "hash": "2509135598633783361"}, {"file": "assets/website/fly-fit-mileage-challenge-3x.png", "hash": "12765225692555743756"}, {"file": "assets/website/fly-fit-view-meal.png", "hash": "698956035848224607"}, {"file": "assets/website/fly-fit-wellness-education.png", "hash": "15602058771633677261"}, {"file": "assets/website/fly-fit-workouts-3x.png", "hash": "15621225909280807100"}, {"file": "assets/website/garmin_connect_ios_icon.png", "hash": "3344047441792947338"}, {"file": "assets/website/mileage challenge lederboard.PNG", "hash": "6488220291873862136"}, {"file": "assets/website/mileage challenge team feed.JPEG", "hash": "5952285546354636073"}, {"file": "assets/website/mileage challenge top view.PNG", "hash": "9792882271690378316"}, {"file": "assets/website/schools/Chadwick_Logo_Grey.svg", "hash": "6600378572638189915"}, {"file": "assets/website/schools/Toplogo.svg", "hash": "5926648597780362399"}, {"file": "assets/website/schools/chadwick.png", "hash": "11030862938380383663"}, {"file": "assets/website/schools/chandler.png", "hash": "3219533889018819111"}, {"file": "assets/website/schools/flintridge.png", "hash": "14074813121884132149"}, {"file": "assets/website/schools/hies red.png", "hash": "3319765074001821651"}, {"file": "assets/website/schools/hies-logo-stacked-white.svg", "hash": "13578283038924116463"}, {"file": "assets/website/schools/hies.png", "hash": "14734563415780858633"}, {"file": "assets/website/schools/logo-horizontal-sm.svg", "hash": "1831263436381463629"}, {"file": "assets/website/schools/ma academy.avif", "hash": "10250335877340807000"}, {"file": "assets/website/schools/manhattan academy.png", "hash": "14356801136952576981"}, {"file": "assets/website/schools/maranatha.png", "hash": "7511634049007575746"}, {"file": "assets/website/schools/puc national.png", "hash": "8929465915536019395"}, {"file": "assets/website/schools/st. rancis high school.png", "hash": "2023594507941238465"}, {"file": "assets/website/schools/wish schools.png", "hash": "8360818097436438886"}, {"file": "assets/website/wearable-steps.webp", "hash": "7948368115319938493"}, {"file": "assets/website/works_with_app_icon.svg", "hash": "4596286323769108594"}, {"file": "assets/website/works_with_apple_health.png", "hash": "8032154399131817328"}, {"file": "assets/website/works_with_fitbit.png", "hash": "10091288330492775635"}, {"file": "assets/website/works_with_health_connect.png", "hash": "13619339265147368024"}, {"file": "babel.config.cjs", "hash": "8361397679296719143"}, {"file": "data/config/app-config-dev.json", "hash": "10969702726298030776"}, {"file": "data/config/app-config-prod.json", "hash": "2404112035266043230"}, {"file": "data/wellness/wellness-blogs-dev.json", "hash": "8009459640465030404"}, {"file": "data/wellness/wellness-blogs-prod.json", "hash": "8009459640465030404"}, {"file": "data/wellness/wellness-quiz-dev.json", "hash": "2879476988234845507"}, {"file": "data/wellness/wellness-quiz-prod.json", "hash": "2879476988234845507"}, {"file": "data/wellness/wellness-quotes-dev.json", "hash": "6988075855346253454"}, {"file": "data/wellness/wellness-quotes-prod.json", "hash": "6988075855346253454"}, {"file": "docs/ARCHITECTURE.md", "hash": "6726044840875377798"}, {"file": "docs/CONTRIBUTING.md", "hash": "17487225079143229300"}, {"file": "docs/DEPLOYMENT.md", "hash": "259125902815825902"}, {"file": "docs/ci-cd-commands.tgn", "hash": "8443744005692171130"}, {"file": "docs/fly-fit-domain-diagram.excalidraw", "hash": "8062605054625854953"}, {"file": "docs/fly-fit-domain-diagram.png", "hash": "18253543709403590787"}, {"file": "docs/fly-fit-fall-2024-roadmap.excalidraw", "hash": "3855552809186532592"}, {"file": "docs/fly-fit-ios-app-icons.excalidraw", "hash": "13188138309392658203"}, {"file": "docs/fly-fit-summer-2024-roadmap.excalidraw", "hash": "17947226595550951984"}, {"file": "docs/fly-fit-winter-2025-roadmap.excalidraw", "hash": "15567242593810604962"}, {"file": "docs/pull_request_template.md", "hash": "370469743508043856"}, {"file": "eas.json", "hash": "4026485811971027038"}, {"file": "eslint.config.mjs", "hash": "9885481208070106618", "deps": ["npm:@eslint/js", "npm:@eslint-community/eslint-plugin-eslint-comments", "npm:@stylistic/eslint-plugin", "npm:@typescript-eslint/parser", "npm:eslint-import-resolver-typescript", "npm:eslint-plugin-ban", "npm:eslint-plugin-import-x", "npm:eslint-plugin-jsx-a11y", "npm:eslint-plugin-no-loops", "npm:eslint-plugin-react", "npm:eslint-plugin-react-compiler", "npm:eslint-plugin-react-hooks", "npm:eslint-plugin-react-hooks-addons", "npm:eslint-plugin-sonarjs", "npm:eslint-plugin-sort", "npm:eslint-plugin-testing-library", "npm:eslint-plugin-unicorn", "npm:eslint-plugin-unused-imports", "npm:globals", "npm:typescript-eslint"]}, {"file": "firebase.json", "hash": "700621768806782380"}, {"file": "firestore.indexes.json", "hash": "16874610003393031451"}, {"file": "firestore.rules", "hash": "6442481993904693621"}, {"file": "google-services.dev.json", "hash": "14361224517068561000"}, {"file": "google-services.json", "hash": "5897951368301379401"}, {"file": "index.js", "hash": "17380799158258259091", "deps": ["npm:core-js", "npm:expo-asset", "npm:react-native-console-time-polyfill", "npm:expo"]}, {"file": "jest.config.cjs", "hash": "14414691225500489250"}, {"file": "metro.config.cjs", "hash": "4672711966741087004", "deps": ["npm:expo", "npm:react-native-reanimated"]}, {"file": "nx.json", "hash": "8528930226195938366"}, {"file": "package-lock.json", "hash": "15897231307041549091"}, {"file": "package.json", "hash": "13452609148715275584", "deps": ["npm:@babel/core", "npm:@babel/plugin-proposal-export-namespace-from", "npm:@babel/plugin-syntax-import-attributes", "npm:@eslint-community/eslint-plugin-eslint-comments", "npm:@eslint/js", "npm:@nx/eslint", "npm:@nx/expo", "npm:@nx/jest", "npm:@nx/react-native", "npm:@nx/vite", "npm:@nx/web", "npm:@semantic-release/changelog", "npm:@semantic-release/git", "npm:@stylistic/eslint-plugin", "npm:@types/eslint", "npm:@types/jest", "npm:@types/luxon", "npm:@types/node", "npm:@types/react", "npm:@types/react-native-vector-icons", "npm:@types/styled-system", "npm:@types/timezoned-date", "npm:@types/uuid", "npm:babel-plugin-module-resolver", "npm:babel-plugin-optional-require", "npm:babel-preset-expo", "npm:concurrently", "npm:cross-env", "npm:dotenv-vault", "npm:eslint", "npm:eslint-config-expo", "npm:eslint-import-resolver-typescript", "npm:eslint-plugin-ban", "npm:eslint-plugin-eslint-comments", "npm:eslint-plugin-import-x", "npm:eslint-plugin-jsx-a11y", "npm:eslint-plugin-no-loops", "npm:eslint-plugin-react", "npm:eslint-plugin-react-compiler", "npm:eslint-plugin-react-hooks", "npm:eslint-plugin-react-hooks-addons", "npm:eslint-plugin-sonarjs", "npm:eslint-plugin-sort", "npm:eslint-plugin-testing-library", "npm:eslint-plugin-unicorn", "npm:eslint-plugin-unused-imports", "npm:expo-atlas", "npm:firebase-admin", "npm:firebase-tools", "npm:globals", "npm:husky", "npm:jest", "npm:jest-expo", "npm:jiti", "npm:minimatch", "npm:mockdate", "npm:nx", "npm:patch-package", "npm:semantic-release", "npm:timezoned-date", "npm:ts-jest", "npm:typescript", "npm:typescript-eslint", "npm:uuid", "npm:vite", "npm:zx", "npm:@babel/runtime", "npm:@expo-google-fonts/epilogue", "npm:@expo/html-elements", "npm:@expo/metro-runtime", "npm:@expo/react-native-action-sheet", "npm:@formatjs/intl-datetimeformat", "npm:@formatjs/intl-displaynames", "npm:@formatjs/intl-getcanonicallocales", "npm:@formatjs/intl-locale", "npm:@formatjs/intl-numberformat", "npm:@formatjs/intl-pluralrules", "npm:@formatjs/intl-relativetimeformat", "npm:@react-native-firebase/app", "npm:@react-native-firebase/auth", "npm:@react-navigation/native", "npm:@react-navigation/native-stack", "npm:@shopify/flash-list", "npm:@tanstack/query-sync-storage-persister", "npm:@tanstack/react-query", "npm:@tanstack/react-query-persist-client", "npm:axios", "npm:babel-plugin-react-compiler", "npm:core-js", "npm:date-fns", "npm:date-fns-tz", "npm:deepmerge", "npm:dequal", "npm:expo", "npm:expo-application", "npm:expo-asset", "npm:expo-background-fetch", "npm:expo-build-properties", "npm:expo-clipboard", "npm:expo-constants", "npm:expo-crypto", "npm:expo-device", "npm:expo-font", "npm:expo-haptics", "npm:expo-health-connect", "npm:expo-image", "npm:expo-image-picker", "npm:expo-insights", "npm:expo-linear-gradient", "npm:expo-linking", "npm:expo-localization", "npm:expo-network", "npm:expo-notifications", "npm:expo-splash-screen", "npm:expo-status-bar", "npm:expo-store-review", "npm:expo-system-ui", "npm:expo-updates", "npm:firebase", "npm:jotai", "npm:jotai-tanstack-query", "npm:loglevel", "npm:luxon", "npm:react", "npm:react-compiler-runtime", "npm:react-dom", "npm:react-error-boundary", "npm:react-native", "npm:react-native-animated-rolling-numbers", "npm:react-native-circular-progress-indicator", "npm:react-native-confetti-cannon", "npm:react-native-console-time-polyfill", "npm:react-native-context-menu-view", "npm:react-native-dotenv", "npm:react-native-draggable-flatlist", "npm:react-native-draglist", "npm:react-native-email-link", "npm:react-native-gesture-handler", "npm:react-native-health", "npm:react-native-health-connect", "npm:react-native-image-keyboard", "npm:react-native-keyboard-controller", "npm:react-native-mmkv", "npm:react-native-paper", "npm:react-native-paper-dates", "npm:react-native-reanimated", "npm:react-native-safe-area-context", "npm:react-native-screens", "npm:react-native-segmented-control-2", "npm:react-native-svg", "npm:react-native-unistyles", "npm:react-native-uuid", "npm:react-native-vector-icons", "npm:react-native-web", "npm:react-native-webview", "npm:react-tracked", "npm:scheduler", "npm:styled-components", "npm:styled-system", "npm:ts-pattern"]}, {"file": "patches/expo-task-manager+12.0.6.patch", "hash": "6520352258401566081"}, {"file": "patches/react-native-animated-rolling-numbers+2.0.0.patch", "hash": "16999861808964287691"}, {"file": "patches/react-native-health+1.19.0.patch", "hash": "15817773747374752844"}, {"file": "patches/react-native-health-connect+3.3.3.patch", "hash": "11338184053822539133"}, {"file": "react-native.config.cjs", "hash": "13355632085193994329"}, {"file": "remoteconfig.template.json", "hash": "1389868326933519382"}, {"file": "scripts/ci-cd-helpers.zx.mjs", "hash": "13915626363220134750"}, {"file": "scripts/ci-cd-update.zx.mjs", "hash": "9531950992067033357", "deps": ["npm:zx"]}, {"file": "scripts/ci-cd.zx.mjs", "hash": "374657039505091165", "deps": ["npm:zx"]}, {"file": "scripts/clean.zx.mjs", "hash": "13970090391016459783", "deps": ["npm:zx"]}, {"file": "scripts/eas-build-post-install.zx.mjs", "hash": "1080111809168454109", "deps": ["npm:zx"]}, {"file": "scripts/eslint/boolean-naming.cjs", "hash": "16468011951534393876"}, {"file": "scripts/export-user-data.appsScript.mjs", "hash": "15262645810587480543"}, {"file": "scripts/export-users.zx.mjs", "hash": "5886290741631703752", "deps": ["npm:zx"]}, {"file": "scripts/get_challenge_report_data.py", "hash": "4384028205528583503"}, {"file": "scripts/helpers.zx.mjs", "hash": "9579268173506775880"}, {"file": "scripts/import-generate-mock-data.zx.mjs", "hash": "2378552433864317206"}, {"file": "scripts/import-mock-data.zx.mjs", "hash": "12558849594390253456", "deps": ["npm:zx"]}, {"file": "scripts/import-users.zx.mjs", "hash": "1355771946471285054", "deps": ["npm:zx", "npm:minimist"]}, {"file": "scripts/import.runbook.md", "hash": "17922782055479857627"}, {"file": "scripts/jestSetup.cjs", "hash": "18445573702986701457", "deps": ["npm:react-native-gesture-handler", "npm:react-native-reanimated", "npm:react-native-safe-area-context"]}, {"file": "scripts/mileage-data-import.appsScript.mjs", "hash": "8454652432770760094"}, {"file": "scripts/setup-agent-script.sh", "hash": "5017501038690297479"}, {"file": "scripts/transform-mock-data.zx.mjs", "hash": "2318450746351339637", "deps": ["npm:zx", "npm:minimist"]}, {"file": "scripts/update-app-config.zx.mjs", "hash": "12714762860790805602", "deps": ["npm:zx", "npm:deepmerge", "npm:dequal", "npm:firebase-admin", "npm:minimist"]}, {"file": "scripts/update-wellness-blogs.zx.mjs", "hash": "8593047905455960313", "deps": ["npm:zx", "npm:dequal", "npm:firebase-admin", "npm:minimist"]}, {"file": "scripts/update-wellness-quiz.zx.mjs", "hash": "11859360619761742158", "deps": ["npm:zx", "npm:dequal", "npm:firebase-admin", "npm:minimist"]}, {"file": "scripts/update-wellness-quotes.zx.mjs", "hash": "4104756198264640459", "deps": ["npm:zx", "npm:dequal", "npm:firebase-admin", "npm:minimist"]}, {"file": "src/App.tsx", "hash": "14253152351569055542", "deps": ["npm:react-native-image-keyboard", "npm:expo-splash-screen", "npm:react-native", "npm:react-native-screens"]}, {"file": "src/BottomStackNavigator.tsx", "hash": "15186339978885353102", "deps": ["npm:react-native-safe-area-context"]}, {"file": "src/RootNavigation.tsx", "hash": "4290395588978470602"}, {"file": "src/api/client.ts", "hash": "6388183524123853105", "deps": ["npm:axios"]}, {"file": "src/api/domain/expoApi.ts", "hash": "15930880463655049034"}, {"file": "src/api/domain/firebaseApi.ts", "hash": "4164608224066278290", "deps": ["npm:axios"]}, {"file": "src/api/domain/fitbitApi.ts", "hash": "16164593807665094213"}, {"file": "src/api/http.ts", "hash": "12469465916042998576"}, {"file": "src/api/index.ts", "hash": "10762976750623927455"}, {"file": "src/assets/Images.tsx", "hash": "2050436155490532374", "deps": ["npm:expo-image"]}, {"file": "src/assets/icons/CalendarIcon.tsx", "hash": "2553362390906012589", "deps": ["npm:react-native-svg"]}, {"file": "src/assets/icons/CircleDotIcon.tsx", "hash": "11346773753791468676", "deps": ["npm:react-native-svg"]}, {"file": "src/assets/icons/ClipboardIcon.tsx", "hash": "10825891210058133965", "deps": ["npm:react-native-svg"]}, {"file": "src/assets/icons/CylingIcon.tsx", "hash": "15139809031054828169", "deps": ["npm:react-native-svg"]}, {"file": "src/assets/icons/DumbbellHorizontalIcon.tsx", "hash": "15619789598337452939", "deps": ["npm:react-native-svg"]}, {"file": "src/assets/icons/DumbbellIcon.tsx", "hash": "17079311532032968668", "deps": ["npm:react-native-svg"]}, {"file": "src/assets/icons/DurationIcon.tsx", "hash": "16773673974078063007", "deps": ["npm:react-native-svg"]}, {"file": "src/assets/icons/FlameIcon.tsx", "hash": "10055197006494380946", "deps": ["npm:react-native-svg"]}, {"file": "src/assets/icons/GroupIcon.tsx", "hash": "16552143119686138106", "deps": ["npm:react-native-svg"]}, {"file": "src/assets/icons/HealthScore.tsx", "hash": "15443260268947194979", "deps": ["npm:react-native-svg"]}, {"file": "src/assets/icons/HiitIcon.tsx", "hash": "3749592034340623752", "deps": ["npm:react-native-svg"]}, {"file": "src/assets/icons/KettleBellIcon.tsx", "hash": "3427038020747081624", "deps": ["npm:react-native-svg"]}, {"file": "src/assets/icons/ManRunningIcon.tsx", "hash": "2237182608243128261", "deps": ["npm:react-native-svg"]}, {"file": "src/assets/icons/MaxIcon.tsx", "hash": "9048105007384251365", "deps": ["npm:react-native-svg"]}, {"file": "src/assets/icons/MealIcon.tsx", "hash": "11011950754673459155", "deps": ["npm:react-native-svg"]}, {"file": "src/assets/icons/MedalIcon.tsx", "hash": "7026320133168174315", "deps": ["npm:react-native-svg"]}, {"file": "src/assets/icons/MemberIcon.tsx", "hash": "11028932316305392480", "deps": ["npm:react-native-svg"]}, {"file": "src/assets/icons/RunIcon.tsx", "hash": "13898867356423253630", "deps": ["npm:react-native-svg"]}, {"file": "src/assets/icons/SpeedometerIcon.tsx", "hash": "12138589494059787162", "deps": ["npm:react-native-svg"]}, {"file": "src/assets/icons/SpeedometerTotalIcon.tsx", "hash": "4698264609481179347", "deps": ["npm:react-native-svg"]}, {"file": "src/assets/icons/SportIcon.tsx", "hash": "652991890392249377", "deps": ["npm:react-native-svg"]}, {"file": "src/assets/icons/StreakIcon.tsx", "hash": "1466791632133724979", "deps": ["npm:react-native-svg"]}, {"file": "src/assets/icons/SwimIcon.tsx", "hash": "7341231387394764683", "deps": ["npm:react-native-svg"]}, {"file": "src/assets/icons/WalkIcon.tsx", "hash": "15772094123771790174", "deps": ["npm:react-native-svg"]}, {"file": "src/assets/icons/YogaIcon.tsx", "hash": "13024689890864605307", "deps": ["npm:react-native-svg"]}, {"file": "src/assets/icons/index.ts", "hash": "137064938933029417"}, {"file": "src/assets/index.ts", "hash": "17112167037020421409"}, {"file": "src/base-components/Accordion.tsx", "hash": "7196721368012189326", "deps": ["npm:react", "npm:react-native"]}, {"file": "src/base-components/AnimatedTextNumber.tsx", "hash": "2200715179497528557", "deps": ["npm:react-native-animated-rolling-numbers", "npm:react-native-reanimated"]}, {"file": "src/base-components/Box.tsx", "hash": "17895017401221097462", "deps": ["npm:styled-components", "npm:styled-system"]}, {"file": "src/base-components/Button.tsx", "hash": "6056393169583022616", "deps": ["npm:react-native-paper", "npm:styled-components", "npm:styled-system"]}, {"file": "src/base-components/ConditionalRender.tsx", "hash": "13560421210556740638", "deps": ["npm:react", "npm:react-native"]}, {"file": "src/base-components/ConditionalWrapper.tsx", "hash": "16467736957827921005", "deps": ["npm:react"]}, {"file": "src/base-components/Confetti.tsx", "hash": "3988466923868921987", "deps": ["npm:react-native-confetti-cannon"]}, {"file": "src/base-components/ContextMenu.tsx", "hash": "15012588420275586154", "deps": ["npm:react-native-context-menu-view", "npm:react-native-paper"]}, {"file": "src/base-components/Divider.tsx", "hash": "12186653500269087063", "deps": ["npm:react-native-paper", "npm:styled-components", "npm:styled-system"]}, {"file": "src/base-components/DropDown.tsx", "hash": "17066433338956550871", "deps": ["npm:react", "npm:react-native", "npm:react-native-paper"]}, {"file": "src/base-components/Grid.tsx", "hash": "10979786802820539063", "deps": ["npm:styled-components", "npm:styled-system"]}, {"file": "src/base-components/Icon.tsx", "hash": "9350250155200347284", "deps": ["npm:react-native-vector-icons"]}, {"file": "src/base-components/KeyboardAwareScrollView.tsx", "hash": "14885488409306752767", "deps": ["npm:react-native"]}, {"file": "src/base-components/LoaderSpinner.tsx", "hash": "9966739511490387235"}, {"file": "src/base-components/LoaderWrapper.tsx", "hash": "8492137249803721848", "deps": ["npm:react"]}, {"file": "src/base-components/LoadingIndicator.tsx", "hash": "5102135088595940340", "deps": ["npm:react-native", "npm:react-native-paper"]}, {"file": "src/base-components/LoadingOverlay.tsx", "hash": "12651776630016764192", "deps": ["npm:react-native"]}, {"file": "src/base-components/PhotoListInput.tsx", "hash": "13208735598997005301", "deps": ["npm:react-native-paper"]}, {"file": "src/base-components/PressableButton.tsx", "hash": "3957071280401836925", "deps": ["npm:react-native"]}, {"file": "src/base-components/RadioButton.tsx", "hash": "16200192135990303397", "deps": ["npm:react-native", "npm:react-native-paper"]}, {"file": "src/base-components/ScaledPressable.tsx", "hash": "5685416372109860185", "deps": ["npm:react", "npm:react-native"]}, {"file": "src/base-components/SearchBar.tsx", "hash": "12072148542617154251", "deps": ["npm:react", "npm:react-native-paper"]}, {"file": "src/base-components/SegmentedButtons.tsx", "hash": "7892858654667635406", "deps": ["npm:react", "npm:react-native-segmented-control-2"]}, {"file": "src/base-components/ShinySkeletonView.tsx", "hash": "14290810056460913545", "deps": ["npm:expo-linear-gradient", "npm:react-native", "npm:react-native-reanimated"]}, {"file": "src/base-components/StickyAboveNavigation.tsx", "hash": "7802415429250022982", "deps": ["npm:react-native", "npm:react-native-paper", "npm:react-native-safe-area-context", "npm:react-native-screens"]}, {"file": "src/base-components/Surface.tsx", "hash": "4745252636844591256", "deps": ["npm:react-native-paper", "npm:styled-components", "npm:styled-system"]}, {"file": "src/base-components/Surface2.tsx", "hash": "5813801120282853222"}, {"file": "src/base-components/SuspenseWrapper.tsx", "hash": "8748913609395327508", "deps": ["npm:react"]}, {"file": "src/base-components/SwipeableRow.tsx", "hash": "5912403632678154848", "deps": ["npm:react", "npm:react-native", "npm:react-native-gesture-handler"]}, {"file": "src/base-components/Switch.tsx", "hash": "7288620784442390240", "deps": ["npm:react-native-paper"]}, {"file": "src/base-components/SwitchWithLabel.tsx", "hash": "8322346633532722270", "deps": ["npm:react-native-paper"]}, {"file": "src/base-components/Text.tsx", "hash": "12590129288871397874", "deps": ["npm:react-native-paper", "npm:styled-components", "npm:styled-system"]}, {"file": "src/base-components/TextInput.tsx", "hash": "7877659962514351182", "deps": ["npm:react", "npm:react-native", "npm:react-native-paper", "npm:styled-components", "npm:styled-system"]}, {"file": "src/base-components/TextInputBare.tsx", "hash": "2393972959056258315", "deps": ["npm:react", "npm:react-native"]}, {"file": "src/base-components/TextLink.tsx", "hash": "18282012894012652149", "deps": ["npm:react", "npm:react-native-paper"]}, {"file": "src/base-components/ThemeOverrideWrapper.tsx", "hash": "5362915283828625599", "deps": ["npm:react", "npm:react-native-paper"]}, {"file": "src/base-components/TouchableHighlight.tsx", "hash": "10516141951329056228", "deps": ["npm:react-native"]}, {"file": "src/base-components/TouchableHighlightLong.tsx", "hash": "6183420483518364363", "deps": ["npm:react", "npm:react-native-reanimated", "npm:react-native"]}, {"file": "src/base-components/Unmount.tsx", "hash": "4503924325858885740"}, {"file": "src/base-components/index.ts", "hash": "14230156467011878510", "deps": ["npm:react-native-reanimated", "npm:@shopify/flash-list", "npm:react-native", "npm:react-native-circular-progress-indicator", "npm:react-native-gesture-handler", "npm:react-native-paper", "npm:react-native-paper-dates", "npm:expo-status-bar"]}, {"file": "src/components/Admin/index.ts", "hash": "4595979742548113990"}, {"file": "src/components/Animation/AnimatedEllipse.tsx", "hash": "4829121226002289332", "deps": ["npm:react", "npm:react-native", "npm:react-native-reanimated", "npm:react-native-svg"]}, {"file": "src/components/Animation/AnimatedScaleView.tsx", "hash": "9660963811818800507"}, {"file": "src/components/Animation/index.ts", "hash": "1018654251735284391"}, {"file": "src/components/Auth/LoginComponent.tsx", "hash": "8224453788406448723", "deps": ["npm:react-native-email-link"]}, {"file": "src/components/Auth/SignUpComponent.tsx", "hash": "14524993891165522220", "deps": ["npm:react", "npm:react-native-email-link"]}, {"file": "src/components/Auth/index.ts", "hash": "2854973654571308134"}, {"file": "src/components/BasePosts/PostCancelOrSend.tsx", "hash": "6238557136679016054"}, {"file": "src/components/BasePosts/PostCard.tsx", "hash": "13556835157792915207"}, {"file": "src/components/BasePosts/PostImage.tsx", "hash": "6004132759312742529"}, {"file": "src/components/BasePosts/PostInput.tsx", "hash": "10942413007967894114", "deps": ["npm:react", "npm:react-native"]}, {"file": "src/components/BasePosts/PostsList.tsx", "hash": "7152969780805616444", "deps": ["npm:@shopify/flash-list", "npm:react"]}, {"file": "src/components/BasePosts/PostsListSkeleton.tsx", "hash": "583738118075562776"}, {"file": "src/components/BasePosts/index.ts", "hash": "9309513749976913091"}, {"file": "src/components/Calendar/CalendarDataSummary.tsx", "hash": "10051541113359944275"}, {"file": "src/components/Calendar/HomeCalendarButtons.tsx", "hash": "*******************"}, {"file": "src/components/Calendar/HomeCalendarDay.tsx", "hash": "3514459923235305115"}, {"file": "src/components/Calendar/HomeCalendarDayProgress.tsx", "hash": "11043759968326626430"}, {"file": "src/components/Calendar/HomeCalendarDayStepsMileage.tsx", "hash": "17818435629876392245"}, {"file": "src/components/Calendar/HomeCalendarFilters.tsx", "hash": "17888803808262259136", "deps": ["npm:react-native-segmented-control-2"]}, {"file": "src/components/Calendar/HomeCalendarLabelArrows.tsx", "hash": "15665387439316041074", "deps": ["npm:react"]}, {"file": "src/components/Calendar/HomeCalendarMonth.tsx", "hash": "5734899641927140682", "deps": ["npm:react"]}, {"file": "src/components/Calendar/HomeCalendarMonthGrid.tsx", "hash": "5485121747912217219", "deps": ["npm:react"]}, {"file": "src/components/Calendar/HomeCalendarMonthGridDay.tsx", "hash": "8720923294064181489"}, {"file": "src/components/Calendar/HomeCalendarStreakHighlight.tsx", "hash": "11306716646882812360", "deps": ["npm:expo-linear-gradient"]}, {"file": "src/components/Calendar/HomeCalendarTabs.tsx", "hash": "6654171329127402126"}, {"file": "src/components/Calendar/HomeCalendarView.tsx", "hash": "17575562304177184428", "deps": ["npm:react"]}, {"file": "src/components/Calendar/HomeCalendarWeek.tsx", "hash": "16235466779831416549", "deps": ["npm:react"]}, {"file": "src/components/Calendar/HomeCalendarWeekDay.tsx", "hash": "15702466938087067314"}, {"file": "src/components/Calendar/HomeCalendarWrapper.tsx", "hash": "11744139038882106101"}, {"file": "src/components/Calendar/index.ts", "hash": "14616124105675979752"}, {"file": "src/components/ChallengePosts/ChallengePostCard.tsx", "hash": "17165647216530775230"}, {"file": "src/components/ChallengePosts/ChallengePostList.tsx", "hash": "15858025565161180477", "deps": ["npm:react"]}, {"file": "src/components/ChallengePosts/CreateChallengePostGroups.tsx", "hash": "6544276338515079928", "deps": ["npm:react"]}, {"file": "src/components/ChallengePosts/CreateChallengePostTeams.tsx", "hash": "824300341083963467", "deps": ["npm:react"]}, {"file": "src/components/ChallengePosts/EditChallengePostGroups.tsx", "hash": "11918405239954655718"}, {"file": "src/components/ChallengePosts/EditChallengePostTeams.tsx", "hash": "*******************"}, {"file": "src/components/ChallengePosts/index.ts", "hash": "9676235039241077943"}, {"file": "src/components/Challenges/ChallengeAcceptReject.tsx", "hash": "14118479641345712424"}, {"file": "src/components/Challenges/ChallengeCardDynamic.tsx", "hash": "15983240043184731298"}, {"file": "src/components/Challenges/ChallengeCardDynamicList.tsx", "hash": "398168355779469338"}, {"file": "src/components/Challenges/ChallengeCardStatic.tsx", "hash": "10536341329186040123"}, {"file": "src/components/Challenges/ChallengeCardStaticList.tsx", "hash": "6822384449705434368"}, {"file": "src/components/Challenges/ChallengeCountdown.tsx", "hash": "11187399533850127297"}, {"file": "src/components/Challenges/ChallengeCreateTrainerMessage.tsx", "hash": "12635290869949767177"}, {"file": "src/components/Challenges/ChallengeDetailsModal.tsx", "hash": "12295021869190838976"}, {"file": "src/components/Challenges/ChallengeFeed.tsx", "hash": "11515050610134726694", "deps": ["npm:react"]}, {"file": "src/components/Challenges/ChallengeGroupMinimal.tsx", "hash": "9097826584675199251"}, {"file": "src/components/Challenges/ChallengeGroupsLeaderboard.tsx", "hash": "8624061670325403372", "deps": ["npm:react"]}, {"file": "src/components/Challenges/ChallengeGroupsList.tsx", "hash": "1739741767739700316", "deps": ["npm:react"]}, {"file": "src/components/Challenges/ChallengeHeaderPhoto.tsx", "hash": "17611557359940284069"}, {"file": "src/components/Challenges/ChallengeIndividualLeaderboard.tsx", "hash": "12651281831460042590", "deps": ["npm:react"]}, {"file": "src/components/Challenges/ChallengeInitializeStage.tsx", "hash": "13263816008864321521"}, {"file": "src/components/Challenges/ChallengeLeaderboard.tsx", "hash": "8615921412252991941"}, {"file": "src/components/Challenges/ChallengeMilestonePopUpModal.tsx", "hash": "10517400403060002540"}, {"file": "src/components/Challenges/ChallengeMilestones.tsx", "hash": "10090837001492291231", "deps": ["npm:react"]}, {"file": "src/components/Challenges/ChallengeParticipantCard.tsx", "hash": "8938157205492574920", "deps": ["npm:react"]}, {"file": "src/components/Challenges/ChallengeParticipantCardModal.tsx", "hash": "3668306272707491084"}, {"file": "src/components/Challenges/ChallengeParticipantCardSlot.tsx", "hash": "17239899282719597566", "deps": ["npm:react"]}, {"file": "src/components/Challenges/ChallengeParticipantList.tsx", "hash": "14402326004304174222", "deps": ["npm:react"]}, {"file": "src/components/Challenges/ChallengeParticipantMinimal.tsx", "hash": "14476633005617303138"}, {"file": "src/components/Challenges/ChallengeProgress.tsx", "hash": "10539964453744409466"}, {"file": "src/components/Challenges/ChallengeProgressCircle.tsx", "hash": "12096002570348891886", "deps": ["npm:react-native"]}, {"file": "src/components/Challenges/ChallengeStageCard.tsx", "hash": "13221204779399329995", "deps": ["npm:react"]}, {"file": "src/components/Challenges/ChallengeStageColorText.tsx", "hash": "9159738020606747194"}, {"file": "src/components/Challenges/ChallengeStageSelector.tsx", "hash": "7865499774984701237"}, {"file": "src/components/Challenges/ChallengeStatBox.tsx", "hash": "5638052246118144034"}, {"file": "src/components/Challenges/ChallengeStats.tsx", "hash": "16040837655586949319"}, {"file": "src/components/Challenges/ChallengeTeamsLeaderboard.tsx", "hash": "13620344063302694258", "deps": ["npm:react"]}, {"file": "src/components/Challenges/ChallengeTeamsList.tsx", "hash": "*******************", "deps": ["npm:react"]}, {"file": "src/components/Challenges/ChallengeViewGroupCard.tsx", "hash": "3634088764580986176", "deps": ["npm:react"]}, {"file": "src/components/Challenges/ChallengeViewGroupModal.tsx", "hash": "2906303504294131327"}, {"file": "src/components/Challenges/ChallengeViewTeamCard.tsx", "hash": "14683168653765042543", "deps": ["npm:react"]}, {"file": "src/components/Challenges/ChallengeViewTeamModal.tsx", "hash": "3363982712904491555"}, {"file": "src/components/Challenges/ChallengeWelcomeModal.tsx", "hash": "*******************"}, {"file": "src/components/Challenges/EditChallenge/EditChallenge.tsx", "hash": "17622075051072693302"}, {"file": "src/components/Challenges/EditChallenge/EditChallengeAddIndividuals.tsx", "hash": "8304309512056009767"}, {"file": "src/components/Challenges/EditChallenge/EditChallengeEditGroupModal.tsx", "hash": "15359743516798407680"}, {"file": "src/components/Challenges/EditChallenge/EditChallengeEditLevelModal.tsx", "hash": "*******************"}, {"file": "src/components/Challenges/EditChallenge/EditChallengeGroup.tsx", "hash": "564469399070162266", "deps": ["npm:react"]}, {"file": "src/components/Challenges/EditChallenge/EditChallengeGroupParticipants.tsx", "hash": "9303347207629724324"}, {"file": "src/components/Challenges/EditChallenge/EditChallengeGroupsList.tsx", "hash": "16589805730682930806", "deps": ["npm:react"]}, {"file": "src/components/Challenges/EditChallenge/EditChallengeGroupsType.tsx", "hash": "15727464937180139236"}, {"file": "src/components/Challenges/EditChallenge/EditChallengeLevel.tsx", "hash": "12551719520002363280", "deps": ["npm:date-fns", "npm:react"]}, {"file": "src/components/Challenges/EditChallenge/EditChallengeLevels.tsx", "hash": "12584815833269685138", "deps": ["npm:react"]}, {"file": "src/components/Challenges/EditChallenge/EditChallengeMetadata.tsx", "hash": "4302769768727520389", "deps": ["npm:react"]}, {"file": "src/components/Challenges/EditChallenge/EditChallengeParticipants.tsx", "hash": "10184583211166286182"}, {"file": "src/components/Challenges/EditChallenge/EditChallengeStages.tsx", "hash": "5283256801691298496", "deps": ["npm:react", "npm:react-native", "npm:react-native-draggable-flatlist", "npm:react-native-draglist"]}, {"file": "src/components/Challenges/EditChallenge/EditChallengeSubmit.tsx", "hash": "1331822080003222693", "deps": ["npm:react"]}, {"file": "src/components/Challenges/EditChallenge/EditChallengeTeam.tsx", "hash": "1832632820910714429"}, {"file": "src/components/Challenges/EditChallenge/EditChallengeTeamCard.tsx", "hash": "14501273333408558855"}, {"file": "src/components/Challenges/EditChallenge/EditChallengeTeamsAndParticipants.tsx", "hash": "16492202951939975724"}, {"file": "src/components/Challenges/EditChallenge/index.ts", "hash": "10575019418391531462"}, {"file": "src/components/Challenges/LiveCountdown.tsx", "hash": "12754185219925654785"}, {"file": "src/components/Challenges/SearchAndAddChallenges.tsx", "hash": "3126781512270637580", "deps": ["npm:react"]}, {"file": "src/components/Challenges/SearchChallenges.tsx", "hash": "6716771582633682283"}, {"file": "src/components/Challenges/index.ts", "hash": "7152157790144808540"}, {"file": "src/components/CoachHome/ClientCard.tsx", "hash": "16672699465107954699"}, {"file": "src/components/CoachHome/ClientDataWarningSticky.tsx", "hash": "9804117836657279946"}, {"file": "src/components/CoachHome/ClientSearch.tsx", "hash": "16124297302197946733"}, {"file": "src/components/CoachHome/CoachStatBox.tsx", "hash": "10109028474100589458"}, {"file": "src/components/CoachHome/index.ts", "hash": "6727859399020510878"}, {"file": "src/components/CopyClipboardButton.tsx", "hash": "11820691484100272906"}, {"file": "src/components/DateAndTimeComponent.tsx", "hash": "3206109849090972493", "deps": ["npm:react"]}, {"file": "src/components/HealthSync/FitbitDebug.tsx", "hash": "15341273657661902907"}, {"file": "src/components/HealthSync/FullHealthSyncReload.tsx", "hash": "9084867281031376695", "deps": ["npm:react"]}, {"file": "src/components/HealthSync/HealthSyncFirstLoadContainer.tsx", "hash": "6241230724917885232"}, {"file": "src/components/HealthSync/HealthSyncModal.tsx", "hash": "289946173815248858", "deps": ["npm:react"]}, {"file": "src/components/HealthSync/HealthSyncSystemIcon.tsx", "hash": "11067092224996310606"}, {"file": "src/components/HealthSync/ManualEntryButton.tsx", "hash": "9740099044376085338"}, {"file": "src/components/HealthSync/TrackingDeviceExplanations.tsx", "hash": "13663916594085098313", "deps": ["npm:expo-linking", "npm:react"]}, {"file": "src/components/HealthSync/TrackingDeviceModal.tsx", "hash": "424467067487827240", "deps": ["npm:react"]}, {"file": "src/components/HealthSync/TrackingDeviceReset.tsx", "hash": "18437014737584427744", "deps": ["npm:react"]}, {"file": "src/components/HealthSync/TrackingDeviceTypeLabel.tsx", "hash": "15399986155117539261"}, {"file": "src/components/HealthSync/index.ts", "hash": "11477645468082313923"}, {"file": "src/components/Home/NotificationPopUpModal.tsx", "hash": "15550705320950247460"}, {"file": "src/components/Home/SummaryNumbers.tsx", "hash": "6004281332414486326"}, {"file": "src/components/Home/SummaryNumbersAllTimeTotals.tsx", "hash": "8731861751991442165"}, {"file": "src/components/Home/SummaryNumbersCurrentMileage.tsx", "hash": "17767714379259853278"}, {"file": "src/components/Home/SummaryNumbersIndividual.tsx", "hash": "3661516839613922853"}, {"file": "src/components/Home/SummaryNumbersWorkoutTotals.tsx", "hash": "566782505073573034"}, {"file": "src/components/Home/index.ts", "hash": "3500735999439610093"}, {"file": "src/components/Icons/AddIcon.tsx", "hash": "13586931455053478782"}, {"file": "src/components/Icons/CreateIcon.tsx", "hash": "11453917815934896693"}, {"file": "src/components/Icons/HelpIcon.tsx", "hash": "17725718571622391202"}, {"file": "src/components/Icons/InfoIcon.tsx", "hash": "16712495955368445809"}, {"file": "src/components/Icons/InfoIconSmall.tsx", "hash": "3357975611637531975"}, {"file": "src/components/Icons/PrivacyPolicyIcon.tsx", "hash": "1967726185163146505"}, {"file": "src/components/Icons/RankIcon.tsx", "hash": "10535891365679820878"}, {"file": "src/components/Icons/ReloadIcon.tsx", "hash": "13442956712027818517"}, {"file": "src/components/Icons/index.ts", "hash": "15908393626681400865"}, {"file": "src/components/ImagePicker.tsx", "hash": "13312600021917353049", "deps": ["npm:expo-image"]}, {"file": "src/components/InviteCode/ChallengeInviteAcceptReject.tsx", "hash": "8744823291652153006", "deps": ["npm:react"]}, {"file": "src/components/InviteCode/InviteCodeContent.tsx", "hash": "3845733622324388828", "deps": ["npm:react"]}, {"file": "src/components/InviteCode/InviteCodeModal.tsx", "hash": "15587745963097904566", "deps": ["npm:react"]}, {"file": "src/components/InviteCode/index.ts", "hash": "2196511722279843447"}, {"file": "src/components/ManualEntry/EditManualEntry.tsx", "hash": "6320475227242752481", "deps": ["npm:react"]}, {"file": "src/components/ManualEntry/index.ts", "hash": "1621455949877968477"}, {"file": "src/components/Meal/CreateMealButton.tsx", "hash": "13114868209579937081"}, {"file": "src/components/Meal/EditMeal.tsx", "hash": "14355553661690098289", "deps": ["npm:react"]}, {"file": "src/components/Meal/MealCard.tsx", "hash": "7528651660010164325", "deps": ["npm:react"]}, {"file": "src/components/Meal/MealStatusIcon.tsx", "hash": "17227138716425473212"}, {"file": "src/components/Meal/index.ts", "hash": "10094460565556071256"}, {"file": "src/components/Notifications/ChallengeNotificationSettings.tsx", "hash": "17280049263730386906"}, {"file": "src/components/Notifications/DebugNotificationsBox.tsx", "hash": "14312301036499944156"}, {"file": "src/components/Notifications/NotificationSettingsHeader.tsx", "hash": "13062999245731981210"}, {"file": "src/components/Notifications/NotificationToggleBell.tsx", "hash": "14669809074164012975", "deps": ["npm:react"]}, {"file": "src/components/Notifications/StreakNotificationSettings.tsx", "hash": "10594296209195796169"}, {"file": "src/components/Notifications/WellnessBlogCategorySettings.tsx", "hash": "12673357114252195336", "deps": ["npm:react"]}, {"file": "src/components/Notifications/WellnessBlogSettings.tsx", "hash": "355974413411474323"}, {"file": "src/components/Notifications/WellnessQuizSettings.tsx", "hash": "15610091845175762632"}, {"file": "src/components/Notifications/WorkoutNotificationSettings.tsx", "hash": "13061576054058120791"}, {"file": "src/components/Notifications/index.ts", "hash": "7137842452950355350"}, {"file": "src/components/OrganizationPosts/EditOrganizationPost.tsx", "hash": "14379077689732809240"}, {"file": "src/components/OrganizationPosts/OrganizationPostCard.tsx", "hash": "17769147451792725467"}, {"file": "src/components/OrganizationPosts/OrganizationPostsList.tsx", "hash": "8547093866791973531", "deps": ["npm:react"]}, {"file": "src/components/OrganizationPosts/index.ts", "hash": "7603378994203028749"}, {"file": "src/components/Organizations/EditOrganization.tsx", "hash": "7958336200782087973", "deps": ["npm:react"]}, {"file": "src/components/Organizations/OrganizationCard.tsx", "hash": "17605883894085561473"}, {"file": "src/components/Organizations/OrganizationList.tsx", "hash": "16717783069199300354"}, {"file": "src/components/Organizations/SearchAndAddOrganizations.tsx", "hash": "11996477283670169362", "deps": ["npm:react"]}, {"file": "src/components/Organizations/SearchOrganizations.tsx", "hash": "6354943218478203907", "deps": ["npm:react"]}, {"file": "src/components/Organizations/index.ts", "hash": "14401185560294166212"}, {"file": "src/components/Participants/SearchAndAddUsers.tsx", "hash": "10150780285144746327", "deps": ["npm:react"]}, {"file": "src/components/Participants/SearchUsers.tsx", "hash": "10113667686974381956"}, {"file": "src/components/Participants/UserCard.tsx", "hash": "7578952364797502825"}, {"file": "src/components/Participants/UserList.tsx", "hash": "10029723768498589880"}, {"file": "src/components/Participants/index.ts", "hash": "1191013249148421577"}, {"file": "src/components/PrivacyPolicyConfirmation.tsx", "hash": "14951301893324975299", "deps": ["npm:react"]}, {"file": "src/components/RenderError.tsx", "hash": "18069406608181483136"}, {"file": "src/components/Screen/BackButton.tsx", "hash": "3453606335135641819"}, {"file": "src/components/Screen/ScreenContent.tsx", "hash": "14165992354049685067"}, {"file": "src/components/Screen/ScreenHeader.tsx", "hash": "1578415898592967757"}, {"file": "src/components/Screen/index.ts", "hash": "11298097631956143458"}, {"file": "src/components/Shared/DashedOutlineHomeButton.tsx", "hash": "7778186223726673284"}, {"file": "src/components/Shared/ManagedModal.tsx", "hash": "17620936471641008747", "deps": ["npm:jotai", "npm:react", "npm:react-native", "npm:react-native-gesture-handler", "npm:react-native-paper", "npm:react-native-safe-area-context"]}, {"file": "src/components/Shared/ModalHeaderWithIcon.tsx", "hash": "15449144733808972289", "deps": ["npm:react-native-vector-icons"]}, {"file": "src/components/Shared/ProgressBar.tsx", "hash": "8498940746974456537"}, {"file": "src/components/Shared/StatusIcon.tsx", "hash": "4354198387193343465"}, {"file": "src/components/Shared/index.ts", "hash": "10653192809102191479"}, {"file": "src/components/Streaks/FirstStreakExplanationModal.tsx", "hash": "15440757483748576492"}, {"file": "src/components/Streaks/MovementStreakContainer.tsx", "hash": "6177963438777627751", "deps": ["npm:react"]}, {"file": "src/components/Streaks/StreakFlame.tsx", "hash": "2069631867335290922"}, {"file": "src/components/Streaks/StreakWeekDays.tsx", "hash": "12746219222852361108"}, {"file": "src/components/Streaks/index.ts", "hash": "2641960476882543296"}, {"file": "src/components/SummaryReports/EditSummaryReport.tsx", "hash": "740870366153706168"}, {"file": "src/components/SummaryReports/EditSummaryReportChallenge.tsx", "hash": "*******************", "deps": ["npm:react"]}, {"file": "src/components/SummaryReports/EditSummaryReportSevenDay.tsx", "hash": "7624859617040791613", "deps": ["npm:react"]}, {"file": "src/components/SummaryReports/SummaryReportCard.tsx", "hash": "14924422371466976948"}, {"file": "src/components/SummaryReports/SummaryReportsList.tsx", "hash": "15420292253817821866"}, {"file": "src/components/SummaryReports/index.ts", "hash": "14932106312191344003"}, {"file": "src/components/Updates/UpdateBanner.tsx", "hash": "1311094949231535702", "deps": ["npm:react-native", "npm:react-native-safe-area-context"]}, {"file": "src/components/Updates/index.ts", "hash": "10231458560604950906"}, {"file": "src/components/User/EditGoalModal.tsx", "hash": "*******************", "deps": ["npm:react"]}, {"file": "src/components/User/EditGoals.tsx", "hash": "3189206416751096705", "deps": ["npm:react"]}, {"file": "src/components/User/EditHealthProfileInfoModal.tsx", "hash": "2228243953563909654", "deps": ["npm:react"]}, {"file": "src/components/User/EditHeightAndStepLengthProps.tsx", "hash": "15587179537153476292", "deps": ["npm:react"]}, {"file": "src/components/User/EditUser.tsx", "hash": "18380401204383126603", "deps": ["npm:react"]}, {"file": "src/components/User/EditUserAdmin.tsx", "hash": "13358897310221182465", "deps": ["npm:react"]}, {"file": "src/components/User/UserProfile.tsx", "hash": "7844218145553172785"}, {"file": "src/components/User/index.ts", "hash": "12591773142337058697"}, {"file": "src/components/Weight/EditWeightPopUp.tsx", "hash": "13588561274471485204", "deps": ["npm:react"]}, {"file": "src/components/Weight/LogWeightButtonContainer.tsx", "hash": "3973641693873785499"}, {"file": "src/components/Weight/WeightContainer.tsx", "hash": "8447656476594424431"}, {"file": "src/components/Weight/WeightSampleCard.tsx", "hash": "742608365142140438"}, {"file": "src/components/Weight/index.ts", "hash": "10666443544393053764"}, {"file": "src/components/WellnessBlog/WellnessBlogContainer.tsx", "hash": "14703430639420686830", "deps": ["npm:expo-linking", "npm:react", "npm:react-native"]}, {"file": "src/components/WellnessBlog/index.ts", "hash": "12387078346421451667"}, {"file": "src/components/WellnessQuiz/WellnessQuizButton.tsx", "hash": "1807437804571052902"}, {"file": "src/components/WellnessQuiz/WellnessQuizContainer.tsx", "hash": "3111878463427694769"}, {"file": "src/components/WellnessQuiz/WellnessQuizModal.tsx", "hash": "8574336128000548709", "deps": ["npm:react"]}, {"file": "src/components/WellnessQuiz/WellnessQuizStats.tsx", "hash": "817392422031728261"}, {"file": "src/components/WellnessQuiz/index.ts", "hash": "3312200220548388829"}, {"file": "src/components/Workout/CreateWorkoutButton.tsx", "hash": "2620263227590930663"}, {"file": "src/components/Workout/EditWorkout.tsx", "hash": "5528035260916392989", "deps": ["npm:expo-linking", "npm:react"]}, {"file": "src/components/Workout/WorkoutCard.tsx", "hash": "7084767109330620218", "deps": ["npm:react"]}, {"file": "src/components/Workout/WorkoutCompletionStatus.tsx", "hash": "10784776043666612021"}, {"file": "src/components/Workout/WorkoutCompletionStatusContent.tsx", "hash": "6189442976150237677"}, {"file": "src/components/Workout/WorkoutLinks.tsx", "hash": "5780593695906777463", "deps": ["npm:react"]}, {"file": "src/components/Workout/WorkoutStatusIcon.tsx", "hash": "1920241013283524389"}, {"file": "src/components/Workout/index.ts", "hash": "12119985938174504045"}, {"file": "src/components/index.ts", "hash": "15030788809584115776"}, {"file": "src/constants/contentCodes.ts", "hash": "11924138172450744383"}, {"file": "src/constants/dateAndTimeConstants.ts", "hash": "1670745267214861403"}, {"file": "src/constants/domain.ts", "hash": "5132823533547665720"}, {"file": "src/constants/domainConfiguration.ts", "hash": "123787180544751024"}, {"file": "src/constants/featureFlags.ts", "hash": "******************"}, {"file": "src/constants/index.ts", "hash": "8788216301054159741"}, {"file": "src/constants/permissions.ts", "hash": "5069109917621335644", "deps": ["npm:react-native-health", "npm:react-native-health-connect"]}, {"file": "src/constants/platformProjectEnvironmentConstants.ts", "hash": "15581163294742518528", "deps": ["npm:expo-application", "npm:expo-constants", "npm:expo-device", "npm:react-native"]}, {"file": "src/constants/storageKeys.ts", "hash": "2533813317534947959"}, {"file": "src/constants/systemIcons.ts", "hash": "740143370448679101"}, {"file": "src/contexts/ExpoUpdater.tsx", "hash": "6981675658358322180", "deps": ["npm:expo-updates", "npm:react"]}, {"file": "src/contexts/actionSheetContext.ts", "hash": "14912953014328730451", "deps": ["npm:expo-linking", "npm:react"]}, {"file": "src/contexts/analyticsContext.ts", "hash": "13256869263529592170"}, {"file": "src/contexts/authContext.ts", "hash": "8689081295887633681", "deps": ["npm:@tanstack/react-query", "npm:jotai", "npm:react"]}, {"file": "src/contexts/authContextEffects.ts", "hash": "1603653734515957832", "deps": ["npm:date-fns", "npm:firebase", "npm:react"]}, {"file": "src/contexts/authContextHealthSync.ts", "hash": "14265560097019204687", "deps": ["npm:@tanstack/react-query", "npm:react"]}, {"file": "src/contexts/authContextLogin.ts", "hash": "9641706175026927500", "deps": ["npm:@react-native-firebase/auth", "npm:@tanstack/react-query", "npm:firebase", "npm:react", "npm:react-native"]}, {"file": "src/contexts/authContextOrganizations.ts", "hash": "9758506306138693916", "deps": ["npm:react"]}, {"file": "src/contexts/authContextWeight.ts", "hash": "4108266752483745601", "deps": ["npm:@tanstack/react-query", "npm:react"]}, {"file": "src/contexts/backgroundTasks.ts", "hash": "2628993447039807695", "deps": ["npm:expo-background-fetch"]}, {"file": "src/contexts/challengeInviteContext.ts", "hash": "3142253234830659265", "deps": ["npm:@tanstack/react-query"]}, {"file": "src/contexts/challengeNotificationPopUpModal.ts", "hash": "11949114692847099833", "deps": ["npm:react"]}, {"file": "src/contexts/challengeOperationsContext.ts", "hash": "12583289716558963788", "deps": ["npm:@tanstack/react-query", "npm:react"]}, {"file": "src/contexts/createChallengeContext.ts", "hash": "5656934178955091315", "deps": ["npm:@tanstack/react-query", "npm:firebase", "npm:jotai", "npm:react"]}, {"file": "src/contexts/createChallengePostContext.ts", "hash": "16764053951467441012", "deps": ["npm:@tanstack/react-query", "npm:react"]}, {"file": "src/contexts/createChallengeTrainerMessage.ts", "hash": "934686440127678674", "deps": ["npm:@tanstack/react-query", "npm:react"]}, {"file": "src/contexts/createMealContext.ts", "hash": "17256217571073360460", "deps": ["npm:react"]}, {"file": "src/contexts/createOrganizationContext.ts", "hash": "17244846232418798200", "deps": ["npm:react"]}, {"file": "src/contexts/createOrganizationPostContext.ts", "hash": "4495691878953720269", "deps": ["npm:react"]}, {"file": "src/contexts/createSummaryReportContext.ts", "hash": "3114855270811734514", "deps": ["npm:@tanstack/react-query", "npm:date-fns", "npm:react"]}, {"file": "src/contexts/createWorkoutContext.ts", "hash": "410307437388665843", "deps": ["npm:react"]}, {"file": "src/contexts/editUserContext.ts", "hash": "8354962319794580799", "deps": ["npm:react"]}, {"file": "src/contexts/firestore/appConfigContext.ts", "hash": "14474297013447262473", "deps": ["npm:firebase", "npm:react"]}, {"file": "src/contexts/firestore/appUpdateContext.ts", "hash": "2215666276381276085", "deps": ["npm:firebase", "npm:react"]}, {"file": "src/contexts/firestore/appUserContext.ts", "hash": "5819089928959838726", "deps": ["npm:@tanstack/react-query", "npm:firebase", "npm:react", "npm:axios"]}, {"file": "src/contexts/firestore/appUserOrganizationContext.ts", "hash": "6395785337478962857", "deps": ["npm:react"]}, {"file": "src/contexts/firestore/challengePostContext.ts", "hash": "6467002543644063700", "deps": ["npm:firebase", "npm:react"]}, {"file": "src/contexts/firestore/challengesContext.ts", "hash": "17736534229027826378", "deps": ["npm:react"]}, {"file": "src/contexts/firestore/firestoreHelpers.ts", "hash": "12989979468684054225", "deps": ["npm:@tanstack/react-query", "npm:firebase", "npm:axios"]}, {"file": "src/contexts/firestore/groupsChallengeContext.ts", "hash": "14406212317214926735", "deps": ["npm:@tanstack/react-query", "npm:firebase", "npm:react"]}, {"file": "src/contexts/firestore/healthDataContext.ts", "hash": "16993854498281993353", "deps": ["npm:@tanstack/react-query", "npm:firebase", "npm:react"]}, {"file": "src/contexts/firestore/index.ts", "hash": "18251462311570609715"}, {"file": "src/contexts/firestore/inviteCodeContext.ts", "hash": "17780640151597672085", "deps": ["npm:firebase"]}, {"file": "src/contexts/firestore/mealsContext.ts", "hash": "18193511152799537161", "deps": ["npm:firebase", "npm:react"]}, {"file": "src/contexts/firestore/movementStreaksContext.ts", "hash": "6984204050374321048", "deps": ["npm:firebase", "npm:react"]}, {"file": "src/contexts/firestore/organizationContext.ts", "hash": "4522317982946832850", "deps": ["npm:firebase", "npm:react"]}, {"file": "src/contexts/firestore/organizationPostContext.ts", "hash": "16196091496278688169", "deps": ["npm:firebase"]}, {"file": "src/contexts/firestore/participantChallengesContext.ts", "hash": "14974780313905387171", "deps": ["npm:firebase", "npm:react"]}, {"file": "src/contexts/firestore/quizProgressContext.ts", "hash": "15776443137077052196", "deps": ["npm:firebase"]}, {"file": "src/contexts/firestore/summaryReportContext.ts", "hash": "7984985760639985263", "deps": ["npm:firebase", "npm:react"]}, {"file": "src/contexts/firestore/useBaseAppUserSearch.ts", "hash": "8803105714317641281", "deps": ["npm:@tanstack/react-query", "npm:react"]}, {"file": "src/contexts/firestore/useFirestoreSearch.ts", "hash": "2240054906167798977", "deps": ["npm:firebase"]}, {"file": "src/contexts/firestore/wellnessBlogsContext.ts", "hash": "1738587588254370209", "deps": ["npm:@tanstack/react-query"]}, {"file": "src/contexts/firestore/wellnessQuizContext.ts", "hash": "10820654182821465246", "deps": ["npm:@tanstack/react-query"]}, {"file": "src/contexts/firestore/wellnessQuotesContext.ts", "hash": "9655301165504309641", "deps": ["npm:@tanstack/react-query"]}, {"file": "src/contexts/firestore/workoutsContext.ts", "hash": "7046140990140449338", "deps": ["npm:firebase", "npm:react"]}, {"file": "src/contexts/fitbitContext.ts", "hash": "2417254230810068900", "deps": ["npm:@tanstack/react-query", "npm:react"]}, {"file": "src/contexts/fitbitHelpers.ts", "hash": "9732657790717534997"}, {"file": "src/contexts/globalNavigationStateContext.ts", "hash": "7357638208252281306", "deps": ["npm:react"]}, {"file": "src/contexts/healthSync.ts", "hash": "7626423124808913615", "deps": ["npm:jotai", "npm:jotai-tanstack-query"]}, {"file": "src/contexts/healthSyncHelpers.ts", "hash": "14078096423112562019", "deps": ["npm:date-fns"]}, {"file": "src/contexts/index.ts", "hash": "13802323410542237660"}, {"file": "src/contexts/localDeviceSettingsContext.ts", "hash": "9437075278640282449", "deps": ["npm:@tanstack/react-query", "npm:jotai", "npm:react"]}, {"file": "src/contexts/localNotificationContext.ts", "hash": "17039455519370094545", "deps": ["npm:jotai"]}, {"file": "src/contexts/persistedSelectedOrganizationIdContext.ts", "hash": "14718667146125944719", "deps": ["npm:jotai"]}, {"file": "src/contexts/persistentEmailContext.ts", "hash": "2765059790815984422", "deps": ["npm:jotai", "npm:react"]}, {"file": "src/contexts/providers/AppProvider.tsx", "hash": "15555583196870382882", "deps": ["npm:@expo/react-native-action-sheet", "npm:@tanstack/react-query", "npm:@tanstack/react-query-persist-client", "npm:firebase", "npm:jotai", "npm:jotai-tanstack-query", "npm:react", "npm:react-native-gesture-handler", "npm:react-native-keyboard-controller", "npm:react-native-paper", "npm:react-native-paper-dates", "npm:react-native-safe-area-context", "npm:styled-components"]}, {"file": "src/contexts/providers/ErrorWrapper.tsx", "hash": "907779304185348525", "deps": ["npm:react", "npm:react-error-boundary"]}, {"file": "src/contexts/providers/FocusManager.tsx", "hash": "17017397259500063913", "deps": ["npm:@tanstack/react-query", "npm:react-native"]}, {"file": "src/contexts/providers/FontProvider.tsx", "hash": "4098755084675490663", "deps": ["npm:@expo-google-fonts/epilogue", "npm:react"]}, {"file": "src/contexts/providers/GlobalEffects.tsx", "hash": "8414722972894617969"}, {"file": "src/contexts/providers/ScreenWrapper.tsx", "hash": "8191401759706616869", "deps": ["npm:react-native", "npm:react-native-paper", "npm:react-native-safe-area-context"]}, {"file": "src/contexts/providers/getErrorScreen.tsx", "hash": "13607674593997290274", "deps": ["npm:@react-navigation/elements"]}, {"file": "src/contexts/providers/index.ts", "hash": "15244405339894414817"}, {"file": "src/contexts/pushNotificationContext.ts", "hash": "8556191776172534533", "deps": ["npm:@tanstack/react-query", "npm:expo-notifications", "npm:react"]}, {"file": "src/contexts/quizProgressContext.ts", "hash": "13944725600583897627", "deps": ["npm:react"]}, {"file": "src/contexts/snacks/SnackContainer.tsx", "hash": "1779805177001784268"}, {"file": "src/contexts/snacks/index.ts", "hash": "2969336886710646880"}, {"file": "src/contexts/snacks/snackContext.ts", "hash": "13627914085936664806", "deps": ["npm:jotai", "npm:react"]}, {"file": "src/contexts/useAllUrlListenersEffect.ts", "hash": "561199535297480036"}, {"file": "src/contexts/useCalendarDayData.ts", "hash": "4510324435978168859", "deps": ["npm:date-fns", "npm:react"]}, {"file": "src/contexts/useInviteCodeUrlListenerEffect.ts", "hash": "5813897111410112470", "deps": ["npm:react"]}, {"file": "src/contexts/useNavContainerConfig.ts", "hash": "2453643063718966832", "deps": ["npm:@react-navigation/native", "npm:expo-linking", "npm:expo-splash-screen", "npm:jotai", "npm:react"]}, {"file": "src/contexts/useUrlParamListenerEffect.ts", "hash": "13586735643177224594", "deps": ["npm:expo-linking", "npm:react"]}, {"file": "src/contexts/useWorkoutShareUrlListenerEffect.ts", "hash": "2436500184885356147", "deps": ["npm:react"]}, {"file": "src/data-hooks/auth.firebase.ts", "hash": "8123839236691458075", "deps": ["npm:@tanstack/react-query", "npm:expo-linking", "npm:firebase", "npm:react"]}, {"file": "src/data-hooks/firestore.firebase.ts", "hash": "9675545238544593744", "deps": ["npm:@tanstack/react-query", "npm:firebase", "npm:react"]}, {"file": "src/data-hooks/index.ts", "hash": "18025148943161731369"}, {"file": "src/data-hooks/storage.firebase.ts", "hash": "17603394245255539374", "deps": ["npm:firebase"]}, {"file": "src/data-hooks/useImageChange.ts", "hash": "2057026978258124272", "deps": ["npm:@tanstack/react-query", "npm:expo-image-picker", "npm:react", "npm:react-native"]}, {"file": "src/hooks/atomWithPersistentStorage.ts", "hash": "11831662956533917258", "deps": ["npm:jotai"]}, {"file": "src/hooks/firestore.ts", "hash": "1676924555469038094", "deps": ["npm:@tanstack/react-query", "npm:firebase", "npm:react"]}, {"file": "src/hooks/firestoreHelpers/index.ts", "hash": "4066584387932305343"}, {"file": "src/hooks/firestoreHelpers/useFirestoreDocument.ts", "hash": "6632394990099759004", "deps": ["npm:@tanstack/react-query", "npm:firebase", "npm:react"]}, {"file": "src/hooks/firestoreHelpers/useFirestoreQuery.ts", "hash": "428569834348525336", "deps": ["npm:@tanstack/react-query", "npm:firebase", "npm:react"]}, {"file": "src/hooks/index.ts", "hash": "14363763431162246522", "deps": ["npm:react-native"]}, {"file": "src/hooks/useBooleanDelay.ts", "hash": "11564903340809398064", "deps": ["npm:react"]}, {"file": "src/hooks/useBooleanLoadOnce.ts", "hash": "4572197564029954314", "deps": ["npm:react"]}, {"file": "src/hooks/useCurrentDate.ts", "hash": "15763050982829441809", "deps": ["npm:jotai", "npm:react"]}, {"file": "src/hooks/useCurrentTimeZone.ts", "hash": "6013065126300202619", "deps": ["npm:expo-localization", "npm:react"]}, {"file": "src/hooks/useDynamicLinkingUrl.ts", "hash": "17319894573424414435", "deps": ["npm:expo-linking"]}, {"file": "src/hooks/useEffectChange.ts", "hash": "9987992484466155028", "deps": ["npm:react"]}, {"file": "src/hooks/useEffectDebounce.ts", "hash": "1334498379695829494", "deps": ["npm:react"]}, {"file": "src/hooks/useEffectIgnoreFirstRender.ts", "hash": "3407008697269923959", "deps": ["npm:react"]}, {"file": "src/hooks/useEmptyArray.ts", "hash": "12310564880475947736", "deps": ["npm:react"]}, {"file": "src/hooks/useHapticCallback.ts", "hash": "11110776207037188147", "deps": ["npm:react"]}, {"file": "src/hooks/useInitialState.ts", "hash": "11668327415904866807", "deps": ["npm:react"]}, {"file": "src/hooks/useInvalidateQueries.ts", "hash": "5452140321917893726", "deps": ["npm:@tanstack/react-query", "npm:react"]}, {"file": "src/hooks/useIsKeyboardOpenState.ts", "hash": "13742217092882444670", "deps": ["npm:react", "npm:react-native"]}, {"file": "src/hooks/useLimitArray.ts", "hash": "13110859940462119969", "deps": ["npm:react"]}, {"file": "src/hooks/useLiveTime.ts", "hash": "3062516320636493345", "deps": ["npm:react", "npm:react-native-reanimated"]}, {"file": "src/hooks/useLogOnChange.ts", "hash": "12228722071485307669", "deps": ["npm:react"]}, {"file": "src/hooks/useLogRenderCount.ts", "hash": "11088753745717941245", "deps": ["npm:react"]}, {"file": "src/hooks/useMemoCache.ts", "hash": "214751649223144795", "deps": ["npm:react"]}, {"file": "src/hooks/useMutationCooldown.ts", "hash": "10521725751400176503", "deps": ["npm:@tanstack/react-query", "npm:react"]}, {"file": "src/hooks/useOnAppStateForeground.ts", "hash": "3588021554755974848", "deps": ["npm:jotai", "npm:react", "npm:react-native"]}, {"file": "src/hooks/useOnMount.ts", "hash": "1431617824931144091", "deps": ["npm:react"]}, {"file": "src/hooks/useOnPressActionSheet.ts", "hash": "10175759806508976552", "deps": ["npm:@expo/react-native-action-sheet", "npm:@tanstack/react-query"]}, {"file": "src/hooks/usePrevious.ts", "hash": "1248425094980262397", "deps": ["npm:react"]}, {"file": "src/hooks/useRefresh.ts", "hash": "4572900282778314911", "deps": ["npm:@tanstack/react-query"]}, {"file": "src/hooks/useScrollToRef.ts", "hash": "865350155702612788", "deps": ["npm:jotai", "npm:react", "npm:react-native"]}, {"file": "src/hooks/useToggleState.ts", "hash": "888872889356405016", "deps": ["npm:react"]}, {"file": "src/hooks/useUpdatedRef.ts", "hash": "7023414150569355406", "deps": ["npm:react"]}, {"file": "src/hooks/useValueIsUndefinedDelay.ts", "hash": "8304899701022437697", "deps": ["npm:react"]}, {"file": "src/pages/Admin/AdminCreateOrganizationScreen.tsx", "hash": "15711585418463227822", "deps": ["npm:react"]}, {"file": "src/pages/Admin/AdminCreateUserScreen.tsx", "hash": "7158173706392935255", "deps": ["npm:react"]}, {"file": "src/pages/Admin/AdminEditUserScreen.tsx", "hash": "9252105259588231879", "deps": ["npm:react"]}, {"file": "src/pages/Admin/AdminHomeScreen.tsx", "hash": "18044933224392856757"}, {"file": "src/pages/Admin/AdminPage.tsx", "hash": "7295845330102850430"}, {"file": "src/pages/Admin/AdminViewAndEditOrganizationScreen.tsx", "hash": "7898489333972424260", "deps": ["npm:react"]}, {"file": "src/pages/Challenges/ChallengeAdminScreen.tsx", "hash": "116702683538399066", "deps": ["npm:@tanstack/react-query", "npm:react"]}, {"file": "src/pages/Challenges/ChallengeEditPostScreen.tsx", "hash": "1291158454808022843", "deps": ["npm:react"]}, {"file": "src/pages/Challenges/ChallengeTrainerMessageScreen.tsx", "hash": "14498903749749756860", "deps": ["npm:react"]}, {"file": "src/pages/Challenges/ChallengesAboutScreen.tsx", "hash": "25180926087302712"}, {"file": "src/pages/Challenges/ChallengesCreateChallengeScreen.tsx", "hash": "5922062948124403137", "deps": ["npm:react"]}, {"file": "src/pages/Challenges/ChallengesPage.tsx", "hash": "9740270215945873073"}, {"file": "src/pages/Challenges/ChallengesScreen.tsx", "hash": "5706027155251188843"}, {"file": "src/pages/Challenges/ViewChallengeScreen.tsx", "hash": "13810650321508916740", "deps": ["npm:react"]}, {"file": "src/pages/Coach/CoachCreateUserScreen.tsx", "hash": "11687496057611079859", "deps": ["npm:react"]}, {"file": "src/pages/Coach/CoachEditUserScreen.tsx", "hash": "9382588806037355477", "deps": ["npm:react"]}, {"file": "src/pages/Coach/CoachHomeScreen.tsx", "hash": "446422764597448009"}, {"file": "src/pages/Coach/CoachPage.tsx", "hash": "9969257521111201228"}, {"file": "src/pages/Coach/CoachViewClientScreen.tsx", "hash": "5197533400236986561"}, {"file": "src/pages/Feed/FeedCreatePostScreen.tsx", "hash": "5706176040367132119", "deps": ["npm:react"]}, {"file": "src/pages/Feed/FeedEditPostScreen.tsx", "hash": "3062528089887546707", "deps": ["npm:react"]}, {"file": "src/pages/Feed/FeedPage.tsx", "hash": "9943538454401305163"}, {"file": "src/pages/Feed/FeedScreen.tsx", "hash": "1639754708867674879", "deps": ["npm:react"]}, {"file": "src/pages/Home/CreateMealScreen.tsx", "hash": "11274409147668514865", "deps": ["npm:react"]}, {"file": "src/pages/Home/CreateSummaryReport.tsx", "hash": "17338453261345417589", "deps": ["npm:react"]}, {"file": "src/pages/Home/CreateWorkoutScreen.tsx", "hash": "8828378572843042405", "deps": ["npm:react"]}, {"file": "src/pages/Home/EditChallengeScreen.tsx", "hash": "18216558209483456589", "deps": ["npm:react"]}, {"file": "src/pages/Home/EditManualEntryScreen.tsx", "hash": "2241509490066949073", "deps": ["npm:react"]}, {"file": "src/pages/Home/EditMealScreen.tsx", "hash": "14781736153979567754", "deps": ["npm:react"]}, {"file": "src/pages/Home/EditProfileScreen.tsx", "hash": "11933047025758844743"}, {"file": "src/pages/Home/EditWorkoutScreen.tsx", "hash": "9177751523214104883", "deps": ["npm:react"]}, {"file": "src/pages/Home/HealthSyncSettingsScreen.tsx", "hash": "8169394258468620989", "deps": ["npm:react"]}, {"file": "src/pages/Home/HomePage.tsx", "hash": "6505418230352890116"}, {"file": "src/pages/Home/HomeScreen.tsx", "hash": "1501505341614432430"}, {"file": "src/pages/Home/LoginLoadingScreen.tsx", "hash": "3693816239414422744", "deps": ["npm:react"]}, {"file": "src/pages/Home/LoginScreen.tsx", "hash": "15913310307786364391"}, {"file": "src/pages/Home/NotificationSettingsScreen.tsx", "hash": "13792656114312290896"}, {"file": "src/pages/Home/PrivacyPolicyConfirmationScreen.tsx", "hash": "570113964258429930"}, {"file": "src/pages/Home/ProfilePage.tsx", "hash": "2415240246821152068"}, {"file": "src/pages/Home/SettingScreen.tsx", "hash": "11148980510206395987", "deps": ["npm:expo-linking"]}, {"file": "src/pages/Home/SignUpScreen.tsx", "hash": "14387404327971157263"}, {"file": "src/pages/Home/SummaryReportsScreen.tsx", "hash": "*******************"}, {"file": "src/pages/Home/WeightScreen.tsx", "hash": "18324992348824885082", "deps": ["npm:react"]}, {"file": "src/pages/index.ts", "hash": "11501570158414572998"}, {"file": "src/types/cardHelper.ts", "hash": "2845607361136994968"}, {"file": "src/types/contexts.ts", "hash": "*******************", "deps": ["npm:firebase"]}, {"file": "src/types/dates.test.ts", "hash": "14957088582324265216"}, {"file": "src/types/dates.ts", "hash": "11858723484025139971", "deps": ["npm:date-fns-tz", "npm:luxon"]}, {"file": "src/types/datesMathOnly.ts", "hash": "10735917790566775307"}, {"file": "src/types/env.d.ts", "hash": "1422661072172383900"}, {"file": "src/types/expo.ts", "hash": "6519815163628789159", "deps": ["npm:expo-device", "npm:expo-network", "npm:expo-notifications", "npm:expo-modules-core"]}, {"file": "src/types/firebase.ts", "hash": "8007995464586248969", "deps": ["npm:firebase"]}, {"file": "src/types/fitbitTypes.ts", "hash": "9887573878880150905"}, {"file": "src/types/healthSync.ts", "hash": "18108277381358542503"}, {"file": "src/types/homeCalendar.ts", "hash": "10826778510442133524"}, {"file": "src/types/importUsers.ts", "hash": "2637140270957045780"}, {"file": "src/types/index.ts", "hash": "16420997335442466063"}, {"file": "src/types/isoDateFormattingHelpers.ts", "hash": "7915271150005648933", "deps": ["npm:date-fns-tz"]}, {"file": "src/types/isoDateGenerateHelpers.ts", "hash": "11593706248739961875"}, {"file": "src/types/isoDateMathOnly.test.ts", "hash": "18192341504134804369"}, {"file": "src/types/isoDateMathOnly.ts", "hash": "18071658151088248600", "deps": ["npm:date-fns"]}, {"file": "src/types/isoDateMonthHelpers.test.ts", "hash": "1628018696562955602"}, {"file": "src/types/isoDateMonthHelpers.ts", "hash": "6830647110100628752"}, {"file": "src/types/isoDateWeekHelpers.test.ts", "hash": "3436259654249749697"}, {"file": "src/types/isoDateWeekHelpers.ts", "hash": "4517119842465228362"}, {"file": "src/types/localDeviceSettings.ts", "hash": "2191862618977020278"}, {"file": "src/types/models/appConfigModel.ts", "hash": "8924237194615450072"}, {"file": "src/types/models/appUpdateModel.ts", "hash": "9012724635143876550"}, {"file": "src/types/models/appUserMetadataModel.ts", "hash": "12053592295182059354"}, {"file": "src/types/models/appUserModel.ts", "hash": "8452236591982642656"}, {"file": "src/types/models/appUsersNotificationsModel.ts", "hash": "3793831174441338080"}, {"file": "src/types/models/authTokenModel.ts", "hash": "15190007083809533316"}, {"file": "src/types/models/challengeBase.ts", "hash": "2700766144547341454"}, {"file": "src/types/models/challengeGroupsModel.ts", "hash": "7414562825233244759"}, {"file": "src/types/models/challengeModel.ts", "hash": "16800248804179662680"}, {"file": "src/types/models/challengePostModel.ts", "hash": "10817932708254618625"}, {"file": "src/types/models/challengeTeamsModel.ts", "hash": "5916023193609472878"}, {"file": "src/types/models/healthDataModel.ts", "hash": "14766554402288273777"}, {"file": "src/types/models/index.d.ts", "hash": "15238891762832667783"}, {"file": "src/types/models/index.ts", "hash": "4587916615372090037"}, {"file": "src/types/models/inviteCodeModel.ts", "hash": "191132528829800738"}, {"file": "src/types/models/mealsModel.ts", "hash": "14071150514603975093"}, {"file": "src/types/models/movementStreakModel.ts", "hash": "5683640900903284706"}, {"file": "src/types/models/organizationModel.ts", "hash": "14577499239433419148"}, {"file": "src/types/models/organizationPostModel.ts", "hash": "12302326391842553980"}, {"file": "src/types/models/quizStreakModel.ts", "hash": "10798582098727841527"}, {"file": "src/types/models/shared.ts", "hash": "15062149700773845857"}, {"file": "src/types/models/summaryReportModel.ts", "hash": "107923907128329580"}, {"file": "src/types/models/wellnessBlogModel.ts", "hash": "711643673210436979"}, {"file": "src/types/models/wellnessQuizModel.ts", "hash": "5519761029849036351"}, {"file": "src/types/models/wellnessQuoteModel.ts", "hash": "4377005687104033301"}, {"file": "src/types/models/workoutModel.ts", "hash": "16308386022307619127"}, {"file": "src/types/pushNotifications.ts", "hash": "7686176900796272371"}, {"file": "src/types/react-native-health-connect.ts", "hash": "12730625691118042522", "deps": ["npm:react-native-health-connect"]}, {"file": "src/types/react-native-health.ts", "hash": "12768782092145771468", "deps": ["npm:react-native-health"]}, {"file": "src/types/react-native-paper.ts", "hash": "488589154710749032", "deps": ["npm:react-native-paper"]}, {"file": "src/types/react-native-svg.ts", "hash": "15020502721479793825"}, {"file": "src/types/react-native.ts", "hash": "9425273862036017996", "deps": ["npm:react-native"]}, {"file": "src/types/react.ts", "hash": "1170705515497775563", "deps": ["npm:react"]}, {"file": "src/types/statusCodes.ts", "hash": "11732376761637269050"}, {"file": "src/types/trackingDeviceTypes.ts", "hash": "1986186266514430512"}, {"file": "src/types/ts-pattern.ts", "hash": "2283103890746357647", "deps": ["npm:ts-pattern"]}, {"file": "src/types/utils.ts", "hash": "4365508140179572310"}, {"file": "src/types/web-sign-up.ts", "hash": "2508821715066490227"}, {"file": "src/utils/dates-timing-conversion/conversionHelpers.ts", "hash": "13596243766067018911"}, {"file": "src/utils/dates-timing-conversion/dateFormatters.ts", "hash": "16365922452714422161", "deps": ["npm:date-fns", "npm:date-fns-tz"]}, {"file": "src/utils/dates-timing-conversion/dateHelpers.test-exclude.ts", "hash": "17827347515253001911"}, {"file": "src/utils/dates-timing-conversion/dateHelpers.ts", "hash": "17052345535647206758", "deps": ["npm:date-fns"]}, {"file": "src/utils/dates-timing-conversion/index.ts", "hash": "8465423386660476569"}, {"file": "src/utils/dates-timing-conversion/timingHelpers.ts", "hash": "10533757624246292585"}, {"file": "src/utils/dates-timing-conversion/unitsConversion.ts", "hash": "2007463096808283706"}, {"file": "src/utils/dates-timing-conversion/validation.ts", "hash": "2645821873125173663"}, {"file": "src/utils/domain/analyticsHelpers.ts", "hash": "4966564786311739971", "deps": ["npm:expo-device", "npm:expo-network"]}, {"file": "src/utils/domain/authHelpers.test-exclude.ts", "hash": "14791421743632646057"}, {"file": "src/utils/domain/authHelpers.ts", "hash": "13328335298556605200"}, {"file": "src/utils/domain/authenticationHelpers.ts", "hash": "14831105220329672567"}, {"file": "src/utils/domain/challengeFormattingHelpers.ts", "hash": "14405470445846524119"}, {"file": "src/utils/domain/challengeHelper.test-exclude.ts", "hash": "14511418159935573362"}, {"file": "src/utils/domain/challengeHelpers.ts", "hash": "4226811176367066135", "deps": ["npm:date-fns"]}, {"file": "src/utils/domain/challengePostHelpers.ts", "hash": "13400672055122709195"}, {"file": "src/utils/domain/challengeStageHelpers.ts", "hash": "8393160579844119573"}, {"file": "src/utils/domain/challengeTransformers.ts", "hash": "2722074725903611453"}, {"file": "src/utils/domain/empty.ts", "hash": "15726174286273666762", "deps": ["npm:date-fns", "npm:firebase"]}, {"file": "src/utils/domain/expoHelpers.ts", "hash": "6936558933681968695", "deps": ["npm:expo-haptics", "npm:expo-store-review", "npm:expo-updates"]}, {"file": "src/utils/domain/homeCalendarHelpers.test-exclude.ts", "hash": "14154385971697772797"}, {"file": "src/utils/domain/homeCalendarHelpers.ts", "hash": "16868285080605364191"}, {"file": "src/utils/domain/index.ts", "hash": "14879655244727601814"}, {"file": "src/utils/domain/mealHelpers.ts", "hash": "10543120393193880026"}, {"file": "src/utils/domain/notificationHelpers.ts", "hash": "6720652256130071664", "deps": ["npm:expo-device", "npm:expo-notifications"]}, {"file": "src/utils/domain/organizationHelpers.ts", "hash": "15014505149279771159"}, {"file": "src/utils/domain/organizationPostHelper.ts", "hash": "4418178877697663246"}, {"file": "src/utils/domain/profileHelpers.test-exclude.ts", "hash": "15021657465412722978"}, {"file": "src/utils/domain/profileHelpers.ts", "hash": "9759493544786185351"}, {"file": "src/utils/domain/searchHelpers.ts", "hash": "11287867389364964622"}, {"file": "src/utils/domain/streakHelpers.ts", "hash": "4649635177793992621"}, {"file": "src/utils/domain/summaryReportHelpers.ts", "hash": "3543977461413774696"}, {"file": "src/utils/domain/workoutHelpers.ts", "hash": "773848713572169801"}, {"file": "src/utils/error/error.ts", "hash": "16473862868701286857", "deps": ["npm:expo", "npm:firebase", "npm:react"]}, {"file": "src/utils/error/index.ts", "hash": "17941352382214026947"}, {"file": "src/utils/firestore/firebase.config.ts", "hash": "15084888875942502465", "deps": ["npm:firebase"]}, {"file": "src/utils/firestore/firestoreHelpers.ts", "hash": "9071284510321207481", "deps": ["npm:firebase"]}, {"file": "src/utils/firestore/index.ts", "hash": "497182475714947440"}, {"file": "src/utils/health/healthConnect.ts", "hash": "18080894906119417714", "deps": ["npm:react-native-health", "npm:react-native-health-connect"]}, {"file": "src/utils/health/healthDataHelpers.ts", "hash": "12350750840055736500"}, {"file": "src/utils/health/healthKit.ts", "hash": "7368350250931015729"}, {"file": "src/utils/health/healthKitHelpers.test-exclude.ts", "hash": "2038732943841171917", "deps": ["npm:date-fns", "npm:react-native-health"]}, {"file": "src/utils/health/healthKitHelpers.ts", "hash": "7756239690234657360", "deps": ["npm:date-fns", "npm:react-native-health"]}, {"file": "src/utils/health/healthStatsHelpers.test-exclude.ts", "hash": "9552431686530228077"}, {"file": "src/utils/health/healthStatsHelpers.ts", "hash": "797206584655276535", "deps": ["npm:date-fns"]}, {"file": "src/utils/health/index.ts", "hash": "10333157218189382034"}, {"file": "src/utils/health/weightSampleHelpers.ts", "hash": "13795462800448635877"}, {"file": "src/utils/index.ts", "hash": "9967236734468320741"}, {"file": "src/utils/logger.ts", "hash": "15912683671775023932", "deps": ["npm:loglevel"]}, {"file": "src/utils/navigation/index.ts", "hash": "17342906795944635722"}, {"file": "src/utils/navigation/linkingHelpers.ts", "hash": "3530463397632630659", "deps": ["npm:@tanstack/react-query", "npm:expo-linking", "npm:react-native"]}, {"file": "src/utils/navigation/navigation.config.ts", "hash": "8168102743870866432", "deps": ["npm:@react-navigation/native", "npm:@react-navigation/native-stack", "npm:date-fns", "npm:react", "npm:react-native-paper"]}, {"file": "src/utils/primitives/arrayHelpers.ts", "hash": "7036205785262818590"}, {"file": "src/utils/primitives/imageHelpers.ts", "hash": "12073066682652055669"}, {"file": "src/utils/primitives/index.ts", "hash": "16094624502226489202"}, {"file": "src/utils/primitives/isDeepEqual.ts", "hash": "1198067424607896207", "deps": ["npm:dequal", "npm:deepmerge"]}, {"file": "src/utils/primitives/mathHelpers.ts", "hash": "6704219312136203799"}, {"file": "src/utils/primitives/mergeHelpers.test.ts", "hash": "7919334735890830879"}, {"file": "src/utils/primitives/mergeHelpers.ts", "hash": "18313820836592366482"}, {"file": "src/utils/primitives/promiseHelpers.ts", "hash": "15834677771341164485"}, {"file": "src/utils/primitives/react-native-health-connect.ts", "hash": "2571613978137409863", "deps": ["npm:react-native-health-connect", ["npm:react-native-health-connect", "dynamic"]]}, {"file": "src/utils/primitives/react-native-health.ts", "hash": "4992375808311473360", "deps": ["npm:react-native-health", ["npm:react-native-health", "dynamic"]]}, {"file": "src/utils/primitives/utils.ts", "hash": "1304165505966440027"}, {"file": "src/utils/primitives/uuid.ts", "hash": "4922127362985694751", "deps": ["npm:react-native-uuid"]}, {"file": "src/utils/react/componentHelpers.tsx", "hash": "18143123204434735201", "deps": ["npm:jotai", "npm:react"]}, {"file": "src/utils/react/contextHelpers.ts", "hash": "15044409227302263571", "deps": ["npm:react", "npm:react-tracked"]}, {"file": "src/utils/react/crypto-shim.ts", "hash": "6367147738133246896", "deps": ["npm:expo-crypto"]}, {"file": "src/utils/react/index.ts", "hash": "12778674617518148072"}, {"file": "src/utils/react/intl-shim.ts", "hash": "9720139249787457450", "deps": [["npm:@formatjs/intl-getcanonicallocales", "dynamic"], ["npm:@formatjs/intl-locale", "dynamic"], ["npm:@formatjs/intl-pluralrules", "dynamic"], ["npm:@formatjs/intl-numberformat", "dynamic"], ["npm:@formatjs/intl-datetimeformat", "dynamic"], ["npm:@formatjs/intl-relativetimeformat", "dynamic"], ["npm:@formatjs/intl-displaynames", "dynamic"]]}, {"file": "src/utils/react/jotaiHelpers.tsx", "hash": "5955065174562109808", "deps": ["npm:jotai", "npm:react"]}, {"file": "src/utils/react/reactNativeHelpers.ts", "hash": "11764596638454607579", "deps": ["npm:expo-clipboard", "npm:react-native"]}, {"file": "src/utils/storage/index.ts", "hash": "8267376018254315690"}, {"file": "src/utils/storage/mmkv.ts", "hash": "10768902779557070998", "deps": ["npm:@tanstack/query-sync-storage-persister", "npm:firebase", "npm:react-native-mmkv"]}, {"file": "src/utils/styling/cssHelpers.ts", "hash": "9027857947076932155"}, {"file": "src/utils/styling/index.ts", "hash": "5395844598510849363"}, {"file": "src/utils/styling/theme.config.ts", "hash": "11277946892795065883", "deps": ["npm:@react-navigation/native", "npm:jotai", "npm:react-native-paper", "npm:styled-system"]}, {"file": "src/utils/styling/unistyles.ts", "hash": "12903806066028743101", "deps": ["npm:react-native-unistyles"]}, {"file": "src/utils/testing/index.ts", "hash": "3073610506735888235"}, {"file": "src/utils/testing/performance.ts", "hash": "13017753631122317047"}, {"file": "src/utils/testing/testingHelpers.ts", "hash": "15179381897888236092", "deps": ["npm:mockdate", ["npm:mockdate", "dynamic"], ["npm:timezoned-date", "dynamic"]]}, {"file": "storage.rules", "hash": "12196265258299480241"}, {"file": "tsconfig.json", "hash": "7192185082650725534"}], "web-sign-up": [{"file": "web-sign-up/index.html", "hash": "8991390894604129482"}, {"file": "web-sign-up/package-lock.json", "hash": "12626764206936315367"}, {"file": "web-sign-up/package.json", "hash": "3141649713544304345", "deps": ["npm:@types/react", "npm:babel-plugin-react-compiler", "npm:globals", "npm:typescript", "npm:vite", "npm:@tanstack/react-query", "npm:dequal", "npm:react", "npm:react-dom", "npm:ts-pattern"]}, {"file": "web-sign-up/src/App.tsx", "hash": "10802166540285079056", "deps": ["npm:@tanstack/react-query", "npm:react"]}, {"file": "web-sign-up/src/Main.tsx", "hash": "9369670023001305863", "deps": ["npm:react", "npm:react-dom"]}, {"file": "web-sign-up/src/components/AdvancedOptionsContainer.tsx", "hash": "9274462525633560882", "deps": ["npm:react"]}, {"file": "web-sign-up/src/components/Button.tsx", "hash": "7574393304205535200"}, {"file": "web-sign-up/src/components/DownloadFlyFitButtons.tsx", "hash": "8740141284009903480"}, {"file": "web-sign-up/src/components/ExistingUserForm.tsx", "hash": "16275405886197272226"}, {"file": "web-sign-up/src/components/InviteCodeContent.tsx", "hash": "6164198935316910139", "deps": ["npm:react", "npm:ts-pattern"]}, {"file": "web-sign-up/src/components/InviteCodeOptions.tsx", "hash": "11036924315894930311", "deps": ["npm:ts-pattern"]}, {"file": "web-sign-up/src/components/NewUserForm.tsx", "hash": "17320953849748814977", "deps": ["npm:react"]}, {"file": "web-sign-up/src/components/RadioButton.tsx", "hash": "18038612437190442138"}, {"file": "web-sign-up/src/components/SignUpForm.tsx", "hash": "11422195738989888744", "deps": ["npm:react"]}, {"file": "web-sign-up/src/components/Spinner.tsx", "hash": "9707970093809029877", "deps": ["npm:ts-pattern"]}, {"file": "web-sign-up/src/components/form/ErrorMessage.tsx", "hash": "12899497747656216194"}, {"file": "web-sign-up/src/components/form/FormField.tsx", "hash": "8230755646297328515"}, {"file": "web-sign-up/src/components/form/SelectFormField.tsx", "hash": "3014088666856195773"}, {"file": "web-sign-up/src/components/form/SuccessMessage.tsx", "hash": "10141872232056523258"}, {"file": "web-sign-up/src/components/form/index.ts", "hash": "285577006860803253"}, {"file": "web-sign-up/src/components/icons/CloseIcon.tsx", "hash": "4347370519942355171"}, {"file": "web-sign-up/src/components/icons/InfoIcon.tsx", "hash": "10354810577752293296"}, {"file": "web-sign-up/src/components/icons/index.ts", "hash": "15584947701380249675"}, {"file": "web-sign-up/src/components/index.ts", "hash": "14022698566966663849"}, {"file": "web-sign-up/src/components/theme.ts", "hash": "5551333575574070812"}, {"file": "web-sign-up/src/config/api.ts", "hash": "6843912967219759847", "deps": ["npm:react"]}, {"file": "web-sign-up/src/config/index.ts", "hash": "13443657969788988525"}, {"file": "web-sign-up/src/hooks/atoms/index.ts", "hash": "13181154417255650518"}, {"file": "web-sign-up/src/hooks/atoms/newUserFormAtom.ts", "hash": "14290861271779628781", "deps": ["npm:jotai", "npm:react"]}, {"file": "web-sign-up/src/hooks/atoms/queryParamsAtom.ts", "hash": "6340529529196726209", "deps": ["npm:jotai", "npm:react"]}, {"file": "web-sign-up/src/hooks/index.ts", "hash": "13704680750348792997"}, {"file": "web-sign-up/src/hooks/useBooleanDelay.ts", "hash": "11564903340809398064", "deps": ["npm:react"]}, {"file": "web-sign-up/src/hooks/useChallengeInviteCodeState.ts", "hash": "14049343224488568813", "deps": ["npm:react", "npm:ts-pattern"]}, {"file": "web-sign-up/src/hooks/useInviteCodeParam.ts", "hash": "10550770887290165688"}, {"file": "web-sign-up/src/hooks/useNewUserFormState.ts", "hash": "10848595296647301763", "deps": ["npm:react"]}, {"file": "web-sign-up/src/hooks/useQueryParamState.ts", "hash": "337250770386333816", "deps": ["npm:jotai", "npm:react"]}, {"file": "web-sign-up/src/hooks/useSignUpOperations.ts", "hash": "416792832183016123", "deps": ["npm:@tanstack/react-query"]}, {"file": "web-sign-up/src/hooks/useSignUpSuccessParamState.ts", "hash": "17224038314903649803", "deps": ["npm:react"]}, {"file": "web-sign-up/src/hooks/useValueIsUndefinedDelay.ts", "hash": "8623494714916606989", "deps": ["npm:react"]}, {"file": "web-sign-up/src/styles.css", "hash": "11441718439007503275"}, {"file": "web-sign-up/src/types/index.ts", "hash": "12964982977932556139"}, {"file": "web-sign-up/src/utils/index.ts", "hash": "8213285748717114530"}, {"file": "web-sign-up/src/utils/utils.ts", "hash": "337375887158937600"}, {"file": "web-sign-up/src/vite-env.d.ts", "hash": "13599593094130918102"}, {"file": "web-sign-up/tsconfig.app.json", "hash": "17677600006572388330"}, {"file": "web-sign-up/tsconfig.json", "hash": "2365188958523842680"}, {"file": "web-sign-up/tsconfig.node.json", "hash": "2111143257794165081"}, {"file": "web-sign-up/vite.config.ts", "hash": "9561543337098588591", "deps": ["npm:vite"]}]}}}