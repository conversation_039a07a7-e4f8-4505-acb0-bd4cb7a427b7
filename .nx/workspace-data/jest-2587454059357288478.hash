{"17082455224399720246": {"targets": {"jest:test": {"command": "jest", "options": {"cwd": ".", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\",\"module\":\"commonjs\",\"customConditions\":null}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^default", {"externalDependencies": ["jest", "jest-expo/web"]}], "outputs": []}}}, "10207253914075750307": {"targets": {"jest:test": {"command": "jest", "options": {"cwd": ".", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\",\"module\":\"commonjs\",\"customConditions\":null}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^default", {"externalDependencies": ["jest", "jest-expo/web"]}], "outputs": []}}}, "6533869963548243293": {"targets": {"jest:test": {"command": "jest", "options": {"cwd": ".", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\",\"module\":\"commonjs\",\"customConditions\":null}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^default", {"externalDependencies": ["jest", "jest-expo/web"]}], "outputs": []}}}, "9796857112469596751": {"targets": {"jest:test": {"command": "jest", "options": {"cwd": ".", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\",\"module\":\"commonjs\",\"customConditions\":null}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^default", {"externalDependencies": ["jest", "jest-expo/web"]}], "outputs": []}}}, "11663658833912520310": {"targets": {"jest:test": {"command": "jest", "options": {"cwd": ".", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\",\"module\":\"commonjs\",\"customConditions\":null}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^default", {"externalDependencies": ["jest", "jest-expo/web"]}], "outputs": []}}}, "14335128758665179657": {"targets": {"jest:test": {"command": "jest", "options": {"cwd": ".", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\",\"module\":\"commonjs\",\"customConditions\":null}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^default", {"externalDependencies": ["jest", "jest-expo/web"]}], "outputs": []}}}, "10519255933175691479": {"targets": {"jest:test": {"command": "jest", "options": {"cwd": ".", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\",\"module\":\"commonjs\",\"customConditions\":null}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^default", {"externalDependencies": ["jest", "jest-expo/web"]}], "outputs": []}}}, "13921761487826394363": {"targets": {"jest:test": {"command": "jest", "options": {"cwd": ".", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\",\"module\":\"commonjs\",\"customConditions\":null}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^default", {"externalDependencies": ["jest", "jest-expo/web"]}], "outputs": []}}}, "11043087926229265695": {"targets": {"jest:test": {"command": "jest", "options": {"cwd": ".", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\",\"module\":\"commonjs\",\"customConditions\":null}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^default", {"externalDependencies": ["jest", "jest-expo/web"]}], "outputs": []}}}, "13731589588756456975": {"targets": {"jest:test": {"command": "jest", "options": {"cwd": ".", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\",\"module\":\"commonjs\",\"customConditions\":null}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^default", {"externalDependencies": ["jest", "jest-expo/web"]}], "outputs": []}}}, "7739328294930099836": {"targets": {"jest:test": {"command": "jest", "options": {"cwd": ".", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\",\"module\":\"commonjs\",\"customConditions\":null}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^default", {"externalDependencies": ["jest", "jest-expo/web"]}], "outputs": []}}}, "455255939685253063": {"targets": {"jest:test": {"command": "jest", "options": {"cwd": ".", "env": {"TS_NODE_COMPILER_OPTIONS": "{\"moduleResolution\":\"node10\",\"module\":\"commonjs\",\"customConditions\":null}"}}, "metadata": {"technologies": ["jest"], "description": "Run Jest Tests", "help": {"command": "npx jest --help", "example": {"options": {"coverage": true}}}}, "cache": true, "inputs": ["default", "^default", {"externalDependencies": ["jest", "jest-expo/web"]}], "outputs": []}}}}