{"run": {"command": "nx run fly-fit:\"typecheck\"", "startTime": "2025-05-26T22:59:43.650Z", "endTime": "2025-05-26T22:59:55.939Z", "inner": false}, "tasks": [{"taskId": "fly-fit:typecheck", "target": "typecheck", "projectName": "fly-fit", "hash": "2335504786232633269", "startTime": "2025-05-26T22:59:43.706Z", "endTime": "2025-05-26T22:59:55.934Z", "params": "", "cacheStatus": "cache-miss", "status": 1}]}