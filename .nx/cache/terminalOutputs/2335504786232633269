
> fly-fit@1.20.23 typecheck
> nx exec -- node --stack-size=65500 ./node_modules/.bin/tsc --project tsconfig.json

[1G[0K[96msrc/App.tsx[0m:[93m28[0m:[93m27[0m - [91merror[0m[90m TS1014: [0mA rest parameter must be last in a parameter list.

[7m28[0m   global.console.error = (...arg, hi: string) => {
[7m  [0m [91m                          ~~~[0m


Found 1 error in src/App.tsx[90m:28[0m

Error: Command failed: "node" "--stack-size=65500" "./node_modules/.bin/tsc" "--project" "tsconfig.json"
[90m    at genericNodeError (node:internal/errors:983:15)[39m
[90m    at wrappedFn (node:internal/errors:537:14)[39m
[90m    at checkExecSyncError (node:child_process:882:11)[39m
[90m    at execSync (node:child_process:954:15)[39m
    at Object.nxExecCommand [90m(/Users/<USER>/git/fly-fit/[39mnode_modules/[4mnx[24m/src/command-line/exec/exec.js:30:38[90m)[39m
[90m    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)[39m
    at async Object.handler [90m(/Users/<USER>/git/fly-fit/[39mnode_modules/[4mnx[24m/src/command-line/exec/command-object.js:11:13[90m)[39m {
  status: [33m2[39m,
  signal: [1mnull[22m,
  output: [ [1mnull[22m, [1mnull[22m, [1mnull[22m ],
  pid: [33m41030[39m,
  stdout: [1mnull[22m,
  stderr: [1mnull[22m
}
[1G[0K⠙[1G[0K