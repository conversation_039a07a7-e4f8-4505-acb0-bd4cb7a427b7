# FlyFit

## Project Overview

FlyFit is a mobile fitness app built with React Native and Expo, using Firebase backend. The app helps users track their fitness activities, participate in challenges, and connect with trainers and other users.

For more detailed technical documentation, please refer to these documentation files in the `docs` directory:

- [Architecture](docs/ARCHITECTURE.md) - High-level app architecture and design patterns
- [Contributing](docs/CONTRIBUTING.md) - Guidelines for contributing to the project
- [Deployment](docs/DEPLOYMENT.md) - Deployment process and procedures

### Key Features

- Health data tracking (steps, mileage, weight)
- Mileage and step challenges
- Wellness content (blogs, quizzes, quotes)
- Workout tracking
- Meal tracking
- Movement streaks
- Trainer/Coach send workouts, manage challenges, create users

### Tech Stack

- **Frontend**: React Native, Expo, TypeScript
- **State Management**: Jotai, React Query
- **Backend**: Firebase (Firestore, Functions, Auth, Storage)
- **Build/Deploy**: EAS (Expo Application Services)
- **Testing**: Jest

### Repository Structure

```bash
fly-fit/
├── assets/                         # Static assets (images, fonts, etc.)
│   ├── screenshots/                # App store screenshots and mockups
│   ├── svgs/                       # SVG icons and graphics
│   └── website/                    # Website-specific assets
├── data/                           # Configuration and content data
│   ├── config/                     # App configuration files (dev/prod)
│   └── wellness/                   # Static wellness content (blogs, quizzes, quotes)
├── docs/                           # Documentation files
│   ├── ARCHITECTURE.d              # System architecture documentation
│   ├── CONTRIBUTING.m              # Contribution guidelines
│   ├── DEPLOYMENT.md               # Deployment instructions
├── functions/                      # Firebase Cloud Functions (Node.js)
│   ├── emails/                     # Email templates (React Email)
│   ├── src/                        # Function source code
│   │   ├── endpoints/              # API endpoint handlers
│   │   ├── endpointHelpers/        # Business logic helpers
│   │   ├── types/                  # TypeScript type definitions
│   │   └── utils/                  # Utility functions
│   ├── report-generation/          # PDF Report Generation Module
│   │   ├── src/                    # React PDF components
│   ├── swagger-docs/               # API Documentation Module (currently unmaintained)
├── scripts/                        # Build and utility scripts
│   ├── ci-cd*.zx.mjs               # CI/CD automation scripts
│   ├── import-*.zx.mjs             # Data import scripts
│   └── *.appsScript.mjs            # Google Apps Script files
├── src/                            # Main React Native App Source Code
│   ├── api/                        # External API interfaces and services
│   ├── base-components/            # Reusable stylized primitive/atomic UI components
│   ├── components/                 # Feature-specific React components
│   │   ├── Admin/                  # Admin panel feature components
│   │   ├── Auth/                   # Auth components
│   │   ├── Challenges/             # Challenge feature components
│   │   ├── HealthSync/             # Health sync feature components
│   │   └── ...                     #
│   ├── contexts/                   # React contexts for state management
│   ├── data-hooks/                 # Data fetching hooks (Firebase)
│   ├── hooks/                      # Custom React primitive hooks
│   ├── pages/                      # Screen/page components
│   ├── types/                      # TypeScript type definitions
│   │   ├── models/                 # Firestore data model types
│   └── utils/                      # Utility functions
│       ├── domain/                 # Business logic utilities
├── web-sign-up/                    # Web Sign-up Application (React/Vite)
│   ├── src/                        # Web app source code
├── package.json                    # Root project dependencies & scripts
├── firebase.json                   # Firebase project configuration
├── firestore.rules                 # Firestore security rules
├── firestore.indexes.json          # Firestore database indexes
```

#### Module Dependencies

The repository contains several independent modules, each with their own `package.json`:

1. **Root Module** (`/package.json`) - Main React Native app with Expo
   - Dependencies: React Native, Expo SDK, Firebase client SDK, Jotai, React Query
   - Scripts: Development, building, testing, deployment automation

2. **Functions Module** (`/functions/package.json`) - Firebase Cloud Functions
   - Dependencies: Firebase Admin SDK, Express, TypeScript, Jest
   - Purpose: Backend API endpoints, data processing, scheduled tasks
   - Structure:
     ```
     functions/
     ├── src/
     │   ├── endpoints/           # HTTP endpoints and event handlers
     │   │   ├── events/          # Firestore triggers
     │   │   ├── requests/        # HTTP request handlers
     │   │   ├── scheduled/       # Cron job functions
     │   │   └── taskFunctions/   # Cloud Task handlers
     │   ├── endpointHelpers/     # Business logic modules
     │   ├── types/               # Shared type definitions
     │   └── utils/               # Utility functions
     ├── emails/                  # React Email templates
     └── dist/                    # Compiled JavaScript output
     ```

3. **Report Generation** (`/functions/report-generation/package.json`) - PDF generation with React PDF
   - Dependencies: @react-pdf/renderer, React, Vite, Recharts
   - Purpose: Generate PDF reports for challenges and wellness data
   - Features: Web preview, CLI generation, chart rendering
   - Build targets: Node.js module, web application, CLI tool

4. **Swagger Docs** (`/functions/swagger-docs/package.json`) - API documentation server
   - Dependencies: swagger-ui-watcher
   - Purpose: Interactive API documentation with live reload
   - Contains: OpenAPI 3.1 specification for all Firebase Functions endpoints

5. **Web Sign-up** (`/web-sign-up/package.json`) - React web application for user registration
   - Dependencies: React, Vite, TanStack Query, TypeScript
   - Purpose: Web-based user registration and invite code handling
   - Build outputs: Separate dev and production builds
   - Features: Form validation, API integration, responsive design

## Getting Started

npx create-nx-workspace@latest fly-fit --preset=react-native --packageManager=npm --nxCloud=skip

### Pre-requisites

Before setting up the project, there are a few pre-requisites that you must have first:

- MacOS
- [Homebrew](https://brew.sh/) - for installing initial tools on MacOS
  - Run the ["Default Tap Cloning"](https://docs.brew.sh/Installation#default-tap-cloning) command in your Terminal to install homebrew
- [Xcode](https://apps.apple.com/us/app/xcode) - for installing iOS build tools
  - When it prompts you what platforms to install, select latest version of iOS
- [GitHub account](https://github.com/FlyBodies/fly-fit) credentials and associated with `fly-fit` - for accessing our Git code and repo
  - Ask to be added to the FlyBodies organization once you have an account
- [Expo account](https://expo.dev/accounts/fly-bodies/projects/fly-fit) credentials and associated with `fly-fit` - for creating builds in Expo/EAS (*NOT needed for local development*)
  - Ask to be added to the FlyBodies organization once you have an account
- [Dotenv account](https://vault.dotenv.org/ui/ui1/project/b0Cexv/environment/2QCZ1b) credentials and associated with `fly-fit` - for accessing our sensitive environment variables
- [Firebase account](https://console.firebase.google.com/u/0/project/fly-fit/overview) Google account associated with `fly-fit` and `fly-fit-dev` projects - for accessing our backend-as-a-service services

The following steps assumes you already have these tools before continuing.

### Project Setup

The following steps take you through how to clone this project and set it up on your local mac machine.

1. Open the Terminal/CLI app on your Mac
1. Initialize your `git` directory if you don't have one already

    - in the Terminal
      - `cd ~` - changes the directory to your home directory
      - `mkdir git` - creates a new directory called `git` in your home directory
      - `cd git` - changes the directory to the `git` directory
1. Using the Terminal, clone this repo from Github

    - `git clone https://github.com/FlyBodies/fly-fit`

1. Install all of the developer tooling using Homebrew

    - `brew bundle`
    - this installs VSCode for your code editor, NVM for installing NodeJS, Android Studio for building Android

1. Run these commands to setup NodeJS

    - `nvm install 22`
    - `nvm use 22`
    - validate it worked by running the `node -v` and seeing it print `22.14.0` (or a higher number, as long as it starts with 22)

1. Open the project in VSCde via VSCode CLI command

    - `code fly-fit`

1. Install the project dependencies

    - `npm install`

1. Run the setup command

    - `npm run setup`
    - follow the prompts to setup your Firebase login, Dotenv login, and Expo login

1. Validate that everything installed from the `setup` command, and re-run and specifics commands that failed

    - if you see red errors when running the `npm run setup:eas` or `npm run setup:firebase` commands, then re-run those commands individually to authenticate those tools

1. Run the xcode select command to ensure the correct Xcode version is selected

    - `sudo xcode-select -s /Applications/Xcode.app/Contents/Developer`

### Running the Project

Now that you have your project setup, you can run the project locally on your machine.

Since we are using native dependencies, you must always have a development build on a simulator or device when running FlyFit. To make a development build for either platform, run these commands:

- `npm run ios:dev`
- `npm run android:dev`

and then when the app has finished building and launched successfully, you will see these controls in your terminal, which can help you open the simulator or reload the app.

  ```sh
  › Press a │ open Android
  › Press i │ open iOS simulator
  › Press w │ open web
  › Press r │ reload app
  › Press m │ toggle menu
  › Press ? │ show all commands
  ```

After you have already done a development build, you can simply run the `npm run start` command *only* to launch the JavaScript development server again, which will serve the live/hot-reloading JavaScript/React changes will keeping the native dependencies the same. This should be the primary way you run/debug the app after you have done a development build one your simulator.

### Running the Backend Locally

We can run the emulated Firebase backend locally to test our backend services.

1. Generate a service account key for the respective environment (dev or prod) following the [Creating Google Keys](#creating-google-keys) section
    - the following steps assume you have it saved at `data/config/gcloud-dev.json` path
1. Run the Firebase emulator
    - `npm run serve` from the root of the project
    - Verify it is running when you see "Starting Firebase Emulator with the exported data..." in the terminal
1. Open up the emulator UI in your browser
    - `http://localhost:4000`

## Advanced Guides

### Source Control

We are using Trunk-Based Development branching strategy. All feature branches are short lived, PRed into the trunk branch of `main`, and then released from the `main` branch.

To ensure the app versioning is consistent, we are using semantic version commit message standards when making PR commits. This will allow the release pipeline to know how to automatically increment the version number of the app. The following are the commit message standards:

- `fix`: bug fix, backwards compatible only changes
- `feat`: new feature, not backwards compatible
- `docs`: documentation only change
- `test`: adding or refactoring tests only
- `ci`: changes to CI/CD configuration files or scripts

All PRs must be reviewed before merging in and must follow this prefix when making a commit message.

### Building for a Release/Deployment

We are currently manually running a script to build the app for release to save on costs, but the actual build process is all automated. You just have to use this CLI and follow the directions when making a build.

- `npm run ci-cd:local`
  - [requires "Creating Google Keys" section](#creating-google-keys)

This command "ci-cd:local" means "Continuous Integration/Continuous Deployment on local machine" instead of another cloud machine, such as the Github servers which cost money.

You MUST be on the `main` branch that is up-to-date with origin (you cannot make a release or build off of a non-main branch currently).

When it runs, you will be prompted with `y` or `n` questions to configure the builds to target specific platforms, environments, or settings. The default values are recommended, but you can change your answer to configure the build that you want if you're testing something specific.

| Command                                             | Recommended/Default  | Notes                                               |
|---------------------------------------------------- |--------------------- |---------------------------------------------------- |
| Do you want to run tests, typecheck, and linter?    | ✅                    | Runs to ensure no static bugs                       |
| Do you want to build for DEV environment?           | ✅                    |                                                     |
| Do you want to build for PROD environment?          | ✅                    |                                                     |
| Do you want to use EAS?                             | ✅                    | 💸 Costs $2/ios $1/android If no, then runs locally  |
| Do you want to deploy, release, and submit builds?  | ❌                    | Only when doing production releases                 |
| Do you want to build for iOS?                       | ✅                    | If no, then skips                                   |
| Do you want to build for Android?                   | ✅                    | If no, then skips                                   |

***Source***: file: `docs/ci-cd-commands.tgn`, loaded into [Tables Generator](https://www.tablesgenerator.com/markdown_tables)

#### Hotfix Releases

The `ci-cd:local` command is used for making a release, which increments the version number of the app. Everytime the version number increments, a manual submission to the app stores must be made.

But, if there are just JavaScript feature updates, then we can make a hotfix release, which does not increment the version number, and does not require a manual submission to the app stores. We do this using EAS Update channels.

Run the following command when wanting to make a hotfix release:

- `npm run ci-cd:local:update`

### Creating Google Keys

To authenticate with Firebase, Google Cloud, and the Google Play Store properly, you must create a service account key to interface with their APIs securely.

1. Go to these Google Cloud console service accounts, linked here ([dev](https://console.cloud.google.com/iam-admin/serviceaccounts/details/104196623765406899492/keys?walkthrough_id=iam--create-service-account-keys&project=fly-fit-dev&supportedpurview=project)) ([prod](https://console.cloud.google.com/iam-admin/serviceaccounts/details/114390985509958980754/keys?walkthrough_id=iam--create-service-account-keys&project=fly-fit&supportedpurview=project))
1. Click the `+ ADD KEY` button, then select `Create new key`
1. For the `Key type`, select `JSON`
1. Click the `CREATE` button
1. Once that file downloads to your computer, move it into this project's root directory
1. Rename the file to `gcloud-dev.json` for the `fly-fit-dev` project, or `gcloud-prod.json` for the `fly-fit` project
1. Move the file to the `data/config/` directory
1. The commands will now refer to these files to securely authenticate you with GCP

### Backing Up Data

Backups are scheduled to run every 7 days and are stored for 60 days. See the [GCP Disaster Recovery page](https://console.cloud.google.com/firestore/databases/-default-/disaster-recovery?project=fly-fit) for these backups.

### Expo/EAS

To register a new device

- `eas device:create`

### Maintenance

To run minor patch upgrades on all packages, run

```sh
npm update
npx expo install --fix
```

To run an incremental minor version upgrades on packages, run

```sh
npx npm-check -u
```

To upgrade major versions of Expo SDK, and sync with Expo versions:

```sh
npm i -g eas-cli
npm install expo@^52.0.0
npx expo install --fix
npx expo-doctor
```

### Android Debugging

- `adb uninstall com.flybodies.flyfitdev`
  - uninstall app from device
- `adb shell pm reset-permissions com.flybodies.flyfitdev`
  - reset permissions
- `nohup emulator -avd Pixel_9_API_35`
  - starts the Android emulator from CLI, running in the background

#### Android Performance Debugging

You can use [Flashlight](https://docs.flashlight.dev/), a locally used web server that connects to the adb, to profile Android apps via your laptop.

1. Install by running `curl https://get.flashlight.dev | bash`
1. In a terminal, run `flashlight measure`
1. Open up `http://localhost:3000/` in a web browser
1. With an Android connected via USB, click the "Start Measuring" to profile the active app

### App Store Submissions

When submitting new versions to the app stores, we must upload new screenshots with updated functionality and design changes being shown. Each app store has specific requirements, so below are the guidelines for each store.

| App Store         | Screenshot Type      | Emulator Device               | OS API  |
|-------------------|----------------------|-------------------------------|---------|
| 🍎 Apple App Store   | iPhone Screenshots   | iPhone 16 device 6.5"         | iOS 18  |
| 🍎 Apple App Store   | iPad Screenshots     | iPad Pro 13-inch (M4)         | iOS 18  |
| 🤖 Google Play Store | Android Phone Screenshots | Google Pixel 4 XL, Android 14 | API 34  |

#### App Store Screenshot Mockups

We are using the free [App Mockup website](https://studio.app-mockup.com/) to generate app store mockups for both Apple and Google app stores.

See the previously saved `.mockup` files in the `assets/screenshots` directory for the most recent mockups to load when modifying the screenshots or design.

Here are the screenshot device types used:

- Apple iPhone 16 Pro Max
- Apple iPad Pro 12.9 Inch
- Google Pixel 4 XL

## Other

### Release Notes Format

For every release of FlyFit to TestFlight and the app store, we will have a set of release notes that we share in the [#proj-fly-fit-app](https://flybodies.slack.com/archives/C06QMR0KDDM) Slack channel telling internal stakeholders what was updated in the given version. This is meant to be descriptive of the feature changes, and not technically focused changelog which is what PRs and the commit history is for.

Here is the following taxonomy to follow when generating the release notes:

- ✨ New Features
- 🐞 Bug Fixes
- 🎨 UI or design updates
- 🚀 Performance improvements
- 🛠️ Improvements or other fixes
