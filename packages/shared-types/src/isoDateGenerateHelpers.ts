import type {IsoDate, IsoDateInterval} from './models';
import {
  convertDateToEndOfDay,
  convertDateToStartOfDay,
  type DateInterval,
  getDateFromIsoDate,
  type TimeZones,
} from './dates';
import {dateToRawTimestamp} from './firebase';

/**
 * Interval representing the start and end of a day
 */
export const getDayIntervalForDate = (date: Date, timeZone: TimeZones): DateInterval => ({
  startDate: convertDateToStartOfDay(date, timeZone),
  endDate: convertDateToEndOfDay(date, timeZone),
});

/**
 * Interval representing the start and end of a day
 */
export const getDayIntervalForIsoDate = (isoDate: IsoDate, timeZone: TimeZones) => {
  const date = getDateFromIsoDate(isoDate, timeZone);
  return getDayIntervalForDate(date, timeZone);
};

export const getDayIntervalForIsoDateTimestamp = (isoDate: IsoDate, timeZone: TimeZones) => {
  const interval = getDayIntervalForIsoDate(isoDate, timeZone);
  return {
    startDate: dateToRawTimestamp(interval.startDate),
    endDate: dateToRawTimestamp(interval.endDate),
  };
};

export const getDateIntervalFromIsoDateInterval = (
  interval: IsoDateInterval,
  timeZone: TimeZones,
) => {
  const startDate = getDateFromIsoDate(interval.startDate, timeZone);
  const endDate = getDateFromIsoDate(interval.endDate, timeZone);
  return {startDate, endDate};
};
