/* eslint-disable @stylistic/no-mixed-operators -- math operatios */
import {differenceInCalendarDays} from 'date-fns';
import type {IsoDate} from './models';
import {fromZonedTime, type TimeZones} from './dates';

const pad = (n: number) => n.toString().padStart(2, '0');

// Format numbers back into an IsoDate (yyyy-MM-dd)
const formatYMD = (year: number, month: number, day: number): IsoDate =>
  `${year}-${pad(month)}-${pad(day)}` as IsoDate;

// Return the number of days in a given month (month is 1-indexed)
const daysInMonth = (year: number, month: number): number => {
  if (month === 2) {
    // February: leap year check
    return year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0) ? 29 : 28;
  }
  const monthDays = [31, -1, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];
  return monthDays[month - 1]!;
};

// Adds months to an IsoDate, adjusting the day if necessary (e.g. 31->30)
export const addMonthsIsoDate = (isoDate: IsoDate, monthsToAdd: number): IsoDate => {
  const [yearStr, monthStr, dayStr] = isoDate.split('-');
  const year = Number(yearStr);
  const month = Number(monthStr); // 1-indexed
  const day = Number(dayStr);

  const totalMonths = year * 12 + (month - 1) + monthsToAdd;
  const newYear = Math.floor(totalMonths / 12);
  const newMonth = (totalMonths % 12) + 1;
  const lastDay = daysInMonth(newYear, newMonth);
  const newDay = Math.min(day, lastDay);
  return formatYMD(newYear, newMonth, newDay);
};

// Convert an IsoDate to a Julian Day Number using a standard algorithm.
const isoDateToJulian = (isoDate: IsoDate): number => {
  const [yearStr, monthStr, dayStr] = isoDate.split('-');
  const Y = Number(yearStr);
  const M = Number(monthStr);
  const D = Number(dayStr);
  const a = Math.floor((14 - M) / 12);
  const y = Y + 4800 - a;
  const m = M + 12 * a - 3;
  return (
    D +
    Math.floor((153 * m + 2) / 5) +
    365 * y +
    Math.floor(y / 4) -
    Math.floor(y / 100) +
    Math.floor(y / 400) -
    32_045
  );
};

// Convert a Julian Day Number back to an IsoDate.
const julianToIsoDate = (jd: number): IsoDate => {
  const a = jd + 32_044;
  const b = Math.floor((4 * a + 3) / 146_097);
  const c = a - Math.floor((146_097 * b) / 4);
  const d = Math.floor((4 * c + 3) / 1461);
  const e = c - Math.floor((1461 * d) / 4);
  const m = Math.floor((5 * e + 2) / 153);
  const day = e - Math.floor((153 * m + 2) / 5) + 1;
  const month = m + 3 - 12 * Math.floor(m / 10);
  const year = 100 * b + d - 4800 + Math.floor(m / 10);
  return formatYMD(year, month, day);
};

// Adds a given number of days to an IsoDate using Julian conversion.
export const addDaysIsoDate = (isoDate: IsoDate, daysToAdd: number): IsoDate => {
  const jd = isoDateToJulian(isoDate);
  return julianToIsoDate(jd + daysToAdd);
};

export const getCalendarDaysDifference = (
  date1: Date,
  date2: Date,
  timeZone: TimeZones,
  isInclusive = false,
) => {
  // Calculate the calendar days difference respective of timezone
  const difference = differenceInCalendarDays(
    fromZonedTime(date1, timeZone),
    fromZonedTime(date2, timeZone),
  );

  // Adjust for inclusivity
  if (isInclusive) {
    if (difference >= 0) {
      return difference + 1; // Positive or zero difference: Include the end date
    }
    return difference - 1; // Negative difference: Include the start date
  }

  return difference;
};

export const getIsoDateDifference = (
  isoDate1: IsoDate,
  isoDate2: IsoDate,
  isInclusive = false,
): number => {
  const jdn1 = isoDateToJulian(isoDate1);
  const jdn2 = isoDateToJulian(isoDate2);
  // Difference in days
  let diff = jdn1 - jdn2;

  // Adjust for inclusive counting.
  if (isInclusive) {
    diff = diff >= 0 ? diff + 1 : diff - 1;
  }

  return diff;
};
/* eslint-enable @stylistic/no-mixed-operators  */
