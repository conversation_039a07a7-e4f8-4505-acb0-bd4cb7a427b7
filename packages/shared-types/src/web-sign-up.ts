import type {
  Challenge,
  ChallengeGroupDocument,
  GetChallengeGroupParticipantCountRequestDTO,
  GetChallengeGroupParticipantCountResponseDTO,
  GetChallengeGroupRequestDTO,
  GetChallengeRequestDTO,
  GetGroupsChallengeParentGroupsRequestDTO,
  GetInviteCodeRequestDTO,
  InviteCodeDocument,
  SignUpAppUserRequestDTO,
  ValidCredentialFormatRequestDTO,
  ValidCredentialFormatResponseDTO,
} from './models';

export enum SignUpOperations {
  isValidNewUserCredentials = 'isValidNewUserCredentials',
  getChallengeById = 'getChallengeById',
  getInviteCodeDocumentById = 'getInviteCodeDocumentById',
  getChallengeGroupById = 'getChallengeGroupById',
  getChallengeGroupParticipantCount = 'getChallengeGroupParticipantCount',
  getGroupsChallengeParentGroups = 'getGroupsChallengeParentGroups',
  signUpAppUser = 'signUpAppUser',
}

export type SignUpOperationRequestMap = {
  [SignUpOperations.isValidNewUserCredentials]: Partial<ValidCredentialFormatRequestDTO>;
  [SignUpOperations.getChallengeById]: Partial<GetChallengeRequestDTO>;
  [SignUpOperations.getInviteCodeDocumentById]: Partial<GetInviteCodeRequestDTO>;
  [SignUpOperations.getChallengeGroupById]: Partial<GetChallengeGroupRequestDTO>;
  [SignUpOperations.getChallengeGroupParticipantCount]: Partial<GetChallengeGroupParticipantCountRequestDTO>;
  [SignUpOperations.getGroupsChallengeParentGroups]: Partial<GetGroupsChallengeParentGroupsRequestDTO>;
  [SignUpOperations.signUpAppUser]: SignUpAppUserRequestDTO;
};

export type SignUpOperationResponseMap = {
  [SignUpOperations.isValidNewUserCredentials]: ValidCredentialFormatResponseDTO;
  [SignUpOperations.getChallengeById]: Challenge;
  [SignUpOperations.getInviteCodeDocumentById]: InviteCodeDocument;
  [SignUpOperations.getChallengeGroupById]: ChallengeGroupDocument;
  [SignUpOperations.getChallengeGroupParticipantCount]: GetChallengeGroupParticipantCountResponseDTO;
  [SignUpOperations.getGroupsChallengeParentGroups]: ChallengeGroupDocument[];
  [SignUpOperations.signUpAppUser]: boolean;
};

export const whenSignUpOperation =
  <Op extends SignUpOperations>(operation: Op) =>
  // @ts-expect-error -- allow narrowing of body type
    (body: {operation?: Op}): body is SignUpOperationRequestMap[Op] =>
      body.operation === operation;
