import type {TimeZones} from './dates';
import type {FitbitSettings} from './models';

export type HealthSyncOptionsBase = {
  endDate: Date;
  startDate: Date;
  timeZone: TimeZones;
};

export type HealthSyncMileageStepOptions = {
  isMileageGpsSourced: boolean;
  stepLengthInches: number;
  fitbitSettings?: FitbitSettings | undefined;
};

export type HealthSyncOptionsDates = HealthSyncOptionsBase & HealthSyncMileageStepOptions;
