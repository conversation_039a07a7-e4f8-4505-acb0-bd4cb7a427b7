import type {IsoDate} from '@types';
import {addDaysIsoDate} from './isoDateMathOnly';

// Helper to parse an IsoDate string into a UTC Date object
const parseIsoDate = (isoDate: IsoDate): Date => {
  const [yearStr, monthStr, dayStr] = isoDate.split('-');
  return new Date(Date.UTC(Number(yearStr), Number(monthStr) - 1, Number(dayStr)));
};

// Helper to format a UTC Date object back into an IsoDate string
const formatIsoDate = (date: Date): IsoDate => {
  const year = date.getUTCFullYear();
  const month = String(date.getUTCMonth() + 1).padStart(2, '0');
  const day = String(date.getUTCDate()).padStart(2, '0');
  return `${year}-${month}-${day}` as IsoDate;
};

// Helper to shift an IsoDate by a given number of days
const shiftIsoDate = (isoDate: IsoDate, days: number): IsoDate => {
  const date = parseIsoDate(isoDate);
  date.setUTCDate(date.getUTCDate() + days);
  return formatIsoDate(date);
};

export type DayIndex = 0 | 1 | 2 | 3 | 4 | 5 | 6;
// Uses Sakamoto's algorithm to compute the day of the week for an IsoDate.
// Returns: 0 = Sunday, 1 = Monday, …, 6 = Saturday.
export const getDayOfWeekIso = (isoDate: IsoDate): DayIndex => {
  const [yearStr, monthStr, dayStr] = isoDate.split('-');
  const year = Number(yearStr);
  const month = Number(monthStr);
  const day = Number(dayStr);
  const t = [0, 3, 2, 5, 0, 3, 5, 1, 4, 6, 2, 4];
  let y = year;
  if (month < 3) y -= 1;
  return ((y +
    Math.floor(y / 4) -
    Math.floor(y / 100) +
    Math.floor(y / 400) +
    t[month - 1]! +
    day) %
    7) as DayIndex;
};

// Returns the Monday (start of week) for the given IsoDate.
export const getStartOfWeekIsoDate = (isoDate: IsoDate): IsoDate => {
  const dow = getDayOfWeekIso(isoDate);
  // Monday is day 1, so subtract the offset from the current day to Monday
  const shift = (dow + 6) % 7;
  return shiftIsoDate(isoDate, -shift);
};

// Returns the Sunday (end of week) for the given IsoDate.
export const getEndOfWeekIsoDate = (isoDate: IsoDate): IsoDate => {
  const dow = getDayOfWeekIso(isoDate);
  // Sunday is day 0. For Monday (1) we add 6, Tuesday (2) add 5, etc.
  const shift = (7 - dow) % 7;
  return shiftIsoDate(isoDate, shift);
};

export const getWeekKey = (isoDate: IsoDate) => getStartOfWeekIsoDate(isoDate);

export const getWeekFromIsoDate = (date: IsoDate) => {
  const startOfWeek = getStartOfWeekIsoDate(date);
  return Array.from({length: 7}, (_, i) => addDaysIsoDate(startOfWeek, i));
};
