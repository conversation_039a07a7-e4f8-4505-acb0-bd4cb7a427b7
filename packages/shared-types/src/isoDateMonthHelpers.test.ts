import {addMonthsIsoDate} from './isoDateMathOnly';
import {
  getEndOfMonthIsoDate,
  getMonthFromIsoDate,
  getStartOfMonthIsoDate,
} from './isoDateMonthHelpers';

describe('month arithmetic', () => {
  it('should return the previous month with the same day', () => {
    const referenceDate = '2024-09-15';
    const actual = addMonthsIsoDate(referenceDate, -1);
    expect(actual).toEqual('2024-08-15');
  });

  it('should return the next month with the same day', () => {
    const referenceDate = '2024-09-15';
    const actual = addMonthsIsoDate(referenceDate, 1);
    expect(actual).toEqual('2024-10-15');
  });

  it('should handle year boundary (start of next year)', () => {
    const referenceDate = '2023-12-15';
    const actual = addMonthsIsoDate(referenceDate, 1);
    expect(actual).toEqual('2024-01-15');
  });

  it('should handle year boundary (previous year)', () => {
    const referenceDate = '2024-01-15';
    const actual = addMonthsIsoDate(referenceDate, -1);
    expect(actual).toEqual('2023-12-15');
  });
});

describe('month boundary checking', () => {
  it('should return the first day of the month', () => {
    const referenceDate = '2024-09-15';
    const actual = getStartOfMonthIsoDate(referenceDate);
    expect(actual).toEqual('2024-09-01');
  });

  it('should return the first day of the month leap year', () => {
    const referenceDate = '2024-02-29';
    const actual = getStartOfMonthIsoDate(referenceDate);
    expect(actual).toEqual('2024-02-01');
  });

  it('should return the last day of the month leap year', () => {
    const referenceDate = '2024-02-20';
    const actual = getEndOfMonthIsoDate(referenceDate);
    expect(actual).toEqual('2024-02-29');
  });

  it('should return the last day of the month', () => {
    const referenceDate = '2024-09-15';
    const actual = getEndOfMonthIsoDate(referenceDate);
    expect(actual).toEqual('2024-09-30');
  });

  it('should handle year boundary (end of year) start of month', () => {
    const referenceDate = '2023-12-15';
    const actual = getStartOfMonthIsoDate(referenceDate);
    expect(actual).toEqual('2023-12-01');
  });

  it('should handle year boundary (end of year) end of month', () => {
    const referenceDate = '2023-12-15';
    const actual = getStartOfMonthIsoDate(referenceDate);
    expect(actual).toEqual('2023-12-01');
  });

  it(' should handle months with 31 days', () => {
    const referenceDate = '2023-07-01';
    const actual = getEndOfMonthIsoDate(referenceDate);
    expect(actual).toEqual('2023-07-31');
  });
});

describe('generating a month of dates', () => {
  it('should return all days in February for a leap year', () => {
    const referenceDate = '2024-02-01';
    const actual = getMonthFromIsoDate(referenceDate);
    const expected = [
      '2024-02-01',
      '2024-02-02',
      '2024-02-03',
      '2024-02-04',
      '2024-02-05',
      '2024-02-06',
      '2024-02-07',
      '2024-02-08',
      '2024-02-09',
      '2024-02-10',
      '2024-02-11',
      '2024-02-12',
      '2024-02-13',
      '2024-02-14',
      '2024-02-15',
      '2024-02-16',
      '2024-02-17',
      '2024-02-18',
      '2024-02-19',
      '2024-02-20',
      '2024-02-21',
      '2024-02-22',
      '2024-02-23',
      '2024-02-24',
      '2024-02-25',
      '2024-02-26',
      '2024-02-27',
      '2024-02-28',
      '2024-02-29',
    ];
    expect(actual).toEqual(expected);
  });

  it('should return all days in February for a non-leap year', () => {
    const referenceDate = '2023-02-01';
    const actual = getMonthFromIsoDate(referenceDate);
    const expected = [
      '2023-02-01',
      '2023-02-02',
      '2023-02-03',
      '2023-02-04',
      '2023-02-05',
      '2023-02-06',
      '2023-02-07',
      '2023-02-08',
      '2023-02-09',
      '2023-02-10',
      '2023-02-11',
      '2023-02-12',
      '2023-02-13',
      '2023-02-14',
      '2023-02-15',
      '2023-02-16',
      '2023-02-17',
      '2023-02-18',
      '2023-02-19',
      '2023-02-20',
      '2023-02-21',
      '2023-02-22',
      '2023-02-23',
      '2023-02-24',
      '2023-02-25',
      '2023-02-26',
      '2023-02-27',
      '2023-02-28',
    ];
    expect(actual).toEqual(expected);
  });

  it('should return all days in a 30-day month (April)', () => {
    const referenceDate = '2023-04-01';
    const actual = getMonthFromIsoDate(referenceDate);
    const expected = [
      '2023-04-01',
      '2023-04-02',
      '2023-04-03',
      '2023-04-04',
      '2023-04-05',
      '2023-04-06',
      '2023-04-07',
      '2023-04-08',
      '2023-04-09',
      '2023-04-10',
      '2023-04-11',
      '2023-04-12',
      '2023-04-13',
      '2023-04-14',
      '2023-04-15',
      '2023-04-16',
      '2023-04-17',
      '2023-04-18',
      '2023-04-19',
      '2023-04-20',
      '2023-04-21',
      '2023-04-22',
      '2023-04-23',
      '2023-04-24',
      '2023-04-25',
      '2023-04-26',
      '2023-04-27',
      '2023-04-28',
      '2023-04-29',
      '2023-04-30',
    ];
    expect(actual).toEqual(expected);
  });

  it('should return all days in a 31-day month (July)', () => {
    const referenceDate = '2023-07-01';
    const actual = getMonthFromIsoDate(referenceDate);
    const expected = [
      '2023-07-01',
      '2023-07-02',
      '2023-07-03',
      '2023-07-04',
      '2023-07-05',
      '2023-07-06',
      '2023-07-07',
      '2023-07-08',
      '2023-07-09',
      '2023-07-10',
      '2023-07-11',
      '2023-07-12',
      '2023-07-13',
      '2023-07-14',
      '2023-07-15',
      '2023-07-16',
      '2023-07-17',
      '2023-07-18',
      '2023-07-19',
      '2023-07-20',
      '2023-07-21',
      '2023-07-22',
      '2023-07-23',
      '2023-07-24',
      '2023-07-25',
      '2023-07-26',
      '2023-07-27',
      '2023-07-28',
      '2023-07-29',
      '2023-07-30',
      '2023-07-31',
    ];
    expect(actual).toEqual(expected);
  });

  it('should handle the year boundary (December to January)', () => {
    const referenceDate = '2023-12-15';
    const actual = getMonthFromIsoDate(referenceDate);
    const expected = [
      '2023-12-01',
      '2023-12-02',
      '2023-12-03',
      '2023-12-04',
      '2023-12-05',
      '2023-12-06',
      '2023-12-07',
      '2023-12-08',
      '2023-12-09',
      '2023-12-10',
      '2023-12-11',
      '2023-12-12',
      '2023-12-13',
      '2023-12-14',
      '2023-12-15',
      '2023-12-16',
      '2023-12-17',
      '2023-12-18',
      '2023-12-19',
      '2023-12-20',
      '2023-12-21',
      '2023-12-22',
      '2023-12-23',
      '2023-12-24',
      '2023-12-25',
      '2023-12-26',
      '2023-12-27',
      '2023-12-28',
      '2023-12-29',
      '2023-12-30',
      '2023-12-31',
    ];
    expect(actual).toEqual(expected);
  });

  it('should return correct results for February at the leap year boundary', () => {
    const referenceDate = '2020-02-29';
    const actual = getMonthFromIsoDate(referenceDate);
    const expected = [
      '2020-02-01',
      '2020-02-02',
      '2020-02-03',
      '2020-02-04',
      '2020-02-05',
      '2020-02-06',
      '2020-02-07',
      '2020-02-08',
      '2020-02-09',
      '2020-02-10',
      '2020-02-11',
      '2020-02-12',
      '2020-02-13',
      '2020-02-14',
      '2020-02-15',
      '2020-02-16',
      '2020-02-17',
      '2020-02-18',
      '2020-02-19',
      '2020-02-20',
      '2020-02-21',
      '2020-02-22',
      '2020-02-23',
      '2020-02-24',
      '2020-02-25',
      '2020-02-26',
      '2020-02-27',
      '2020-02-28',
      '2020-02-29',
    ];
    expect(actual).toEqual(expected);
  });
});
