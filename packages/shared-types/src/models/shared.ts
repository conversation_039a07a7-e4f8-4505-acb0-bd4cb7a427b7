import type {RawTimestamp} from '../firebase';

/**
 * UUID string type
 */
export type UUIDString = `${string}-${string}-${string}-${string}-${string}`;

export type UUIDStringDoc = {
  id: UUIDString;
};

export type UUIDRequestDTO = {
  id: UUIDString;
};

export type DocumentIdTypeDoc = {
  id: DocumentIdType;
};

export const DUMMY_UUID: UUIDString = '00000000-0000-0000-0000-000000000000';

export const isObjectWithId = (obj: unknown): obj is UUIDStringDoc =>
  typeof obj === 'object' && !!obj && 'id' in obj && typeof obj.id === 'string';

export type SnakeCase3Id = `${string}_${string}_${string}`;

/**
 * `yyyy-MM` date representation
 */
export type IsoMonth = `${number}-${number}`;

export type SnakeCase2Id = `${string}_${string}`;

export type DocumentIdType =
  | UUIDString
  | SnakeCase3Id
  | SnakeCase2Id
  | 'default'
  | `${string}Data`
  | `${string}Debounce`
  | `${number}`
  | `F-${string}`
  | IsoMonth;

export type ShardId = `${number}`;

export type ShardDocument = {
  id: ShardId;
};

/**
 * `yyyy-MM-dd` date representation
 */
export type IsoDate = `${number}-${number}-${number}`;

export const isIsoDate = (date: unknown): date is IsoDate =>
  typeof date === 'string' && date.split('-').length === 3;

export type IsoDateInterval = {
  endDate: IsoDate;
  startDate: IsoDate;
};

export type IsoDateFull = `${number}-${number}-${number}T${number}:${number}:${number}.${number}Z`;

export type IsoTime = `${number}:${number}:${number}.${number}`; // HH:mm:ss.SSS

export type ParticipantBase = UUIDStringDoc;

export type ParticipantBaseOld = {
  email: string;
};

export type ImageUrl =
  `https://firebasestorage.googleapis.com/v0/b/fly-fit.appspot.com/o/images%2F${string}`;

export type BasePost = {
  id: UUIDString;
  content: string;
  authorUserId: UUIDString;
  createdDateTime: RawTimestamp;
  lastModifiedDateTime: RawTimestamp;
  imageUrl?: ImageUrl;
};

export type FullIsoDateString =
  `${number}-${number}-${number}T${number}:${number}:${number}.${number}Z`;

export type DateDTO = {
  date: FullIsoDateString;
};

/**
 * DTO for passing IsoDate format dates
 */
export type IsoDateDTO = {
  isoDate: IsoDate;
};

export const dateToFullIsoDateString = (date: Date) => date.toISOString() as FullIsoDateString;

export const dateToFullIsoDateStringObj = (date: Date) => ({
  date: dateToFullIsoDateString(date),
});

export type OgImageRequestDTO = {
  url: string;
};

export type PushNotificationBaseData<Type, Other> = {
  type: Type;
} & Other;

export type PushNotificationBase<D = Record<string, unknown>> = {
  body: string;
  data?: D;
  title: string;
  to: string;
  userId: UUIDString;
};

export type PushNotificationConfig = {
  isDisabled?: boolean;
  timeOfDay?: IsoTime;
  intervalInMilliseconds?: number;
};

export type LocalNotificationContentData<
  RouteType extends string,
  NotificationType extends string,
> = {
  id: string;
  redirectTo?: RouteType;
  redirectToParams?: Record<string, unknown>;
  type: NotificationType;
};

export type LocalNotificationContent<RouteType extends string, NotificationType extends string> = {
  body: string;
  data: LocalNotificationContentData<RouteType, NotificationType>;
  date: Date;
  title: string;
};

export type StreakValues = {
  completedIsoDate: IsoDate;
  userId: UUIDString;
};

export type DeleteableDocument = {
  deletedTimestamp?: RawTimestamp;
  id: DocumentIdType;
  isSkipDeleteHook?: boolean;
};

export type StreakDocument = DeleteableDocument &
  StreakValues & {
    id: SnakeCase2Id;
  };

export const buildStreakDocumentDocumentId = (
  userId: UUIDString,
  completedIsoDate: IsoDate,
): SnakeCase2Id => `${userId}_${completedIsoDate}`;
