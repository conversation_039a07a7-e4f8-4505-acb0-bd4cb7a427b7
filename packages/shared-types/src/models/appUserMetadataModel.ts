import type {RawTimestamp} from '../firebase';

export type MetadataHealthDataSummary = {
  id: 'healthData';
  totalDistanceMeters: number;
  totalStepsCount: number;
  maxStepsInOneDay?: number;
  maxStepsInOneDayDateTime?: RawTimestamp | undefined;
  lastUpdateTimestamp: number;
};

export type MetadataDebounceType = {
  lastUpdateTimestamp: number;
  isInProgress: boolean;
};

export type MetadataMovementStreakDebounce = MetadataDebounceType & {
  id: 'streakDebounce';
};

export type MetadataHealthDataDebounce = MetadataDebounceType & {
  id: 'healthDataDebounce';
};

export type MetadatAppUserDocument =
  | MetadataHealthDataSummary
  | MetadataMovementStreakDebounce
  | MetadataHealthDataDebounce;
