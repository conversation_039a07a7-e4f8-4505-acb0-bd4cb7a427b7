import type {TimeZones} from '../dates';
import type {RawTimestamp} from '../firebase';
import type {ChallengeJoinPayload} from './inviteCodeModel';
import type {ImageUrl, IsoDate, ParticipantBase, UUIDString} from './shared';

export enum ChallengeType {
  DISTANCE = 'MILEAGE',
  STEP = 'STEP',
  OTHER = 'OTHER',
}

export const allSupportedChallengeTypes = [ChallengeType.DISTANCE, ChallengeType.STEP]; // Object.values(ChallengeType);

export enum ChallengeGroupingType {
  INDIVIDUAL = 'INDIVIDUAL',
  TEAMS = 'TEAMS',
  GROUPS = 'GROUPS',
}

export enum ChallengeInviteStatus {
  PENDING = 'PENDING',
  ACCEPTED = 'ACCEPTED',
  REJECTED = 'REJECTED',
}

const CHALLENGE_INVITE_STRING = {
  [ChallengeInviteStatus.PENDING]: 'Pending',
  [ChallengeInviteStatus.ACCEPTED]: 'Accepted',
  [ChallengeInviteStatus.REJECTED]: 'Rejected',
};

export const getChallengeInviteString = (type: ChallengeInviteStatus) =>
  CHALLENGE_INVITE_STRING[type];

export type ChallengeParticipantGoals = {
  dailyGoalMileageInMeters?: number;
  dailyGoalStepCount?: number;
};

export type DailyDataValues = {
  steps: number;
  distanceMeters: number;
  // exerciseMinutes: number;
};

export type DailyDataValueMapping = Record<IsoDate, DailyDataValues>;

export type DailyDataValueObject = {
  dailyData?: DailyDataValueMapping;
};

export enum ChallengeParticipantRole {
  OWNER = 'OWNER',
  CAPTAIN = 'CAPTAIN',
  PARTICIPANT = 'PARTICIPANT',
}

const ROLE_DISPLAY_NAMES = {
  [ChallengeParticipantRole.OWNER]: 'Owner',
  [ChallengeParticipantRole.CAPTAIN]: 'Captain',
  [ChallengeParticipantRole.PARTICIPANT]: 'Participant',
};

export const getChallengeParticipantRoleDisplayName = (role: ChallengeParticipantRole): string =>
  ROLE_DISPLAY_NAMES[role] || 'Participant';

export type ChallengeParticipantBase = ParticipantBase &
  ChallengeParticipantGoals & {
    lastLoggedDistanceMeters?: number;
    lastLoggedSteps?: number;
    lastLoggedDateTime: RawTimestamp;
    inviteStatus: ChallengeInviteStatus;
    /**
     * The role of the participant (associated with permissions) in the challenge
     *
     *   - OWNER = the owner and editor of the challenge (typically a FlyBodies trainer)
     *   - CAPTAIN = the manager of a team
     *   - PARTICIPANT = any other participant (undefined is assumed to be PARTICIPANT)
     */
    role?: ChallengeParticipantRole;
  };

export type ChallengeStage = {
  id: UUIDString;
  /**
   * Starts at 1
   */
  order: number;
  name: string;
  startedDateTime: RawTimestamp;
  endedDateTime: RawTimestamp;
  type: 'primary' | 'secondary';
};

export type ChallengeBase = {
  id: UUIDString;
  type: ChallengeType;
  challengeName: string;
  bannerImageUrl?: ImageUrl;
  editorIds: UUIDString[];
  createdDateTime: RawTimestamp;
  startedDateTime: RawTimestamp;
  endedDateTime: RawTimestamp;
  lastUpdatedDateTime: RawTimestamp;
  isChallengeEndNotificationSent?: true;
  stages?: ChallengeStage[];
  timeZone?: TimeZones;
  maxTeamSize?: number;
  flags?: {
    isDraft?: boolean;
    isChallengeRolesEnabled?: boolean;
  };
};

const FRIENDLY_CHALLENGE_TYPES = {
  [ChallengeType.DISTANCE]: 'Mileage',
  [ChallengeType.STEP]: 'Steps',
  [ChallengeType.OTHER]: 'Other',
};

export const getChallengeTypeFriendly = (type: ChallengeType) => FRIENDLY_CHALLENGE_TYPES[type];

const CHALLENGE_TYPE_ICONS = {
  [ChallengeType.DISTANCE]: 'ruler',
  [ChallengeType.STEP]: 'foot-print',
  [ChallengeType.OTHER]: 'foot-print',
} as const;

export const getChallengeTypeIcon = (type: ChallengeType) => CHALLENGE_TYPE_ICONS[type];

export type ChallengeDataExportRequestDTO = {
  challengeId: UUIDString;
};

export type ChallengeDataExportResponseDTO = {
  link: string;
  message: string;
};

/**
 * Defines all possible permissions for challenge operations
 */
export enum ChallengePermission {
  // Challenge management permissions
  EDIT_CHALLENGE = 'EDIT_CHALLENGE',
  // Participant management permissions
  INVITE_TEAM_PARTICIPANTS = 'INVITE_TEAM_PARTICIPANTS',
  REMOVE_TEAM_PARTICIPANTS = 'REMOVE_TEAM_PARTICIPANTS',
  EDIT_TEAM_DETAILS = 'EDIT_TEAM_DETAILS',
}

/**
 * Maps each role to its set of permissions
 */
const CHALLENGE_ROLE_PERMISSIONS: Record<ChallengeParticipantRole, ChallengePermission[]> = {
  [ChallengeParticipantRole.OWNER]: Object.values(ChallengePermission),
  [ChallengeParticipantRole.CAPTAIN]: [
    ChallengePermission.INVITE_TEAM_PARTICIPANTS,
    ChallengePermission.REMOVE_TEAM_PARTICIPANTS,
    ChallengePermission.EDIT_TEAM_DETAILS,
  ],
  [ChallengeParticipantRole.PARTICIPANT]: [],
};

/**
 * Checks if a role has a specific permission
 * @param role The role to check
 * @param permission The permission to check for
 * @returns True if the role has the permission, false otherwise
 */
export const hasChallengeRolePermission = (
  role: ChallengeParticipantRole,
  permission: ChallengePermission,
): boolean => CHALLENGE_ROLE_PERMISSIONS[role].includes(permission);

/**
 * Checks if a participant has a specific permission
 * @param participant The challenge participant
 * @param permission The permission to check for
 * @returns True if the participant has the permission, false otherwise
 */
export const hasParticipantPermission = (
  participant: ChallengeParticipantBase | undefined | null,
  permission: ChallengePermission,
): boolean => {
  if (!participant) return false;

  // Default to PARTICIPANT role if none is specified
  const role = participant.role || ChallengeParticipantRole.PARTICIPANT;

  // Only accepted participants have permissions
  if (participant.inviteStatus !== ChallengeInviteStatus.ACCEPTED) return false;

  return hasChallengeRolePermission(role, permission);
};

/**
 * Checks if a participant can perform a specific operation on a challenge
 * @param participant The challenge participant
 * @param permission The permission required for the operation
 * @param targetTeamId Optional team ID for operations that involve a team
 * @returns True if the participant can perform the operation, false otherwise
 */
export const canPerformChallengeOperation = (
  participant: ChallengeParticipantBase | undefined | null,
  permission: ChallengePermission,
  options?: {
    targetTeamId?: UUIDString | undefined;
  },
): boolean => {
  if (!participant) return false;

  // Check if participant has accepted the invite
  if (participant.inviteStatus !== ChallengeInviteStatus.ACCEPTED) return false;

  // Default to PARTICIPANT role if none is specified
  const role = participant.role || ChallengeParticipantRole.PARTICIPANT;

  // For team captains, check if they're operating on their own team
  if (role === ChallengeParticipantRole.CAPTAIN && options?.targetTeamId) {
    // If participant is a captain of the target team, they have special permissions for that team
    // Note: We need to cast here because ChallengeParticipant extends ChallengeParticipantBase with teamId
    const participantWithTeam = participant as {teamId?: UUIDString};
    const isCaptainOfTargetTeam = participantWithTeam.teamId === options.targetTeamId;

    if (
      isCaptainOfTargetTeam &&
      (permission === ChallengePermission.EDIT_TEAM_DETAILS ||
        permission === ChallengePermission.INVITE_TEAM_PARTICIPANTS)
    ) {
      return true;
    }
  }

  // For all other cases, check the standard role permissions
  return hasChallengeRolePermission(role, permission);
};

export enum ChallengeOperations {
  RECALCULATE_GROUP = 'RECALCULATE_GROUP',
  RECALCULATE_LEVEL = 'RECALCULATE_LEVEL',
  RECALCULATE_ALL_GROUPS = 'RECALCULATE_ALL_GROUPS',
  DELETE_CHALLENGE = 'DELETE_CHALLENGE',
  DELETE_CHALLENGE_GROUP = 'DELETE_CHALLENGE_GROUP',
  GET_CHALLENGE_GROUP_CHILDREN = 'GET_CHALLENGE_GROUP_CHILDREN',
  LEAVE_CHALLENGE = 'LEAVE_CHALLENGE',
  JOIN_CHALLENGE = 'JOIN_CHALLENGE',
}

type ChallengeOperationRecalculateGroup = {
  operation: ChallengeOperations.RECALCULATE_GROUP;
  challengeId: UUIDString;
  groupId: UUIDString;
};

type ChallengeOperationRecalculateGroupLevel = {
  operation: ChallengeOperations.RECALCULATE_LEVEL;
  challengeId: UUIDString;
  level: number;
};

type ChallengeOperationRecalculateAllGroups = {
  operation: ChallengeOperations.RECALCULATE_ALL_GROUPS;
  challengeId: UUIDString;
};

type ChallengeOperationDeleteChallenge = {
  operation: ChallengeOperations.DELETE_CHALLENGE;
  challengeId: UUIDString;
};

type ChallengeOperationDeleteChallengeGroup = {
  operation: ChallengeOperations.DELETE_CHALLENGE_GROUP;
  challengeId: UUIDString;
  groupId: UUIDString;
};

type ChallengeOperationGetChallengeGroupChildren = {
  operation: ChallengeOperations.GET_CHALLENGE_GROUP_CHILDREN;
  challengeId: UUIDString;
  parentGroupId: UUIDString;
};

export type ChallengeLeaveRequest = {
  challengeId: UUIDString;
  userId: UUIDString;
};

type ChallengeOperationLeaveChallenge = {
  operation: ChallengeOperations.LEAVE_CHALLENGE;
} & ChallengeLeaveRequest;

export type ChallengeJoinRequest = {
  userId: UUIDString;
} & ChallengeJoinPayload;

export type ChallengeJoinResponse = {
  isTeamCaptain?: boolean | undefined;
  teamNameJoined?: string | undefined;
};

export type ChallengeOperationJoin = {
  operation: ChallengeOperations.JOIN_CHALLENGE;
} & ChallengeJoinRequest;

export type ChallengeOperationRequestTypes =
  | ChallengeOperationRecalculateGroup
  | ChallengeOperationRecalculateGroupLevel
  | ChallengeOperationRecalculateAllGroups
  | ChallengeOperationDeleteChallenge
  | ChallengeOperationDeleteChallengeGroup
  | ChallengeOperationGetChallengeGroupChildren
  | ChallengeOperationLeaveChallenge
  | ChallengeOperationJoin
  | {operation?: undefined};
