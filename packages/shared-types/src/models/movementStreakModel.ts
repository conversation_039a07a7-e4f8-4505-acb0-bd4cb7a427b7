import type {IsoDate, SnakeCase2Id, StreakDocument, StreakValues, UUIDString} from './shared';

export type MovementStreakValues = StreakValues & {
  hasMetDistanceGoal: boolean;
  hasMetStepGoal: boolean;
  hasMetWorkoutGoal: boolean;
};

export type MovementStreakDocument = StreakDocument & MovementStreakValues;

export const buildMovementStreakDocumentId = (
  userId: UUIDString,
  completedIsoDate: IsoDate,
): SnakeCase2Id => `${userId}_${completedIsoDate}`;
