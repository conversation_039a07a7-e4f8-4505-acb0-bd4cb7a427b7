import type {DocumentIdType} from './shared';

export type QuizOption = {
  answer: string; // The possible answer text
  isCorrect?: true; // Whether this option is the correct answer
};

export type WellnessQuiz = {
  id: DocumentIdType;
  question: string; // The quiz question
  options: QuizOption[];
  explanation?: string; // Optional explanation for the entire question
  order: number; // Monotonically increasing number to order the questions
  tags?: string[]; // Optional tags for the question
  createdDateTime?: string;
  lastModifiedDateTime?: string;
};
