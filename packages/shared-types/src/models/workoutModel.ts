import type {RawTimestamp} from '../firebase';
import type {ImageUrl, ParticipantBaseOld, UUIDString} from './shared';

export enum WorkoutType {
  STRENGTH = 'STRENGTH',
  CARDIO = 'CARDIO',
  MOBILITY = 'MOBILITY',
  WEIGHTS = 'WEIGHTS',
  RUN = 'RUN',
  CYCLING = 'CYCLING',
  SWIM = 'SWIM',
  YOGA = 'YOGA',
  WALK = 'WALK',
  HIIT = 'HIIT',
  SPORT = 'SPORT',
  MAX = 'MAX',
}

// ORDERED list of how to display workout types
export const allWorkoutTypesToDisplay = [
  WorkoutType.WALK,
  WorkoutType.RUN,
  WorkoutType.WEIGHTS,
  WorkoutType.HIIT,
  WorkoutType.YOGA,
  WorkoutType.SWIM,
  WorkoutType.CYCLING,
  WorkoutType.SPORT,
  WorkoutType.MAX,
] as const;

type DisplayWorkoutTypes = (typeof allWorkoutTypesToDisplay)[number];

export const filterWorkoutTypes = (type: WorkoutType) =>
  allWorkoutTypesToDisplay.includes(type as DisplayWorkoutTypes);

export type WorkoutLink = {
  url: string;
  label: string;
  id: UUIDString;
};

export type Workout = {
  // user controlled fields
  workoutName: string;
  type: WorkoutType[];
  notes: string;
  trainerIds: UUIDString[];
  participantIds: UUIDString[];
  isCompleted?: true;
  links?: WorkoutLink[];
  images?: ImageUrl[];
  // metadata fields
  id: UUIDString;
  createdDateTime: RawTimestamp;
  startedDateTime: RawTimestamp;
  endedDateTime: RawTimestamp;
  lastUpdatedDateTime: RawTimestamp;
  /**
   * If present, indicates the parent workout of this workout,
   * implying this is a child workout given by a trainer.
   */
  parentWorkoutId?: UUIDString;
  copyWorkoutId?: UUIDString;
  childrenWorkoutIds?: string[];
};

export type WorkoutOld = Omit<Workout, 'trainerIds' | 'participantIds'> & {
  participants: ParticipantBaseOld[];
  trainers: ParticipantBaseOld[];
};

export const isOldWorkout = (workout: Partial<Workout>): workout is WorkoutOld =>
  'participants' in workout && 'trainers' in workout;

const FRIENDLY_WORKOUT_TYPES = {
  [WorkoutType.STRENGTH]: '🏋️ Strength',
  [WorkoutType.CARDIO]: '🏃 Cardio',
  [WorkoutType.MOBILITY]: '🤸‍♀️ Mobility',
  [WorkoutType.WEIGHTS]: '🏋️ Weights',
  [WorkoutType.RUN]: '🏃 Run',
  [WorkoutType.WALK]: '🚶 Walk',
  [WorkoutType.CYCLING]: '🚴 Cycling',
  [WorkoutType.SWIM]: '🏊 Swim',
  [WorkoutType.YOGA]: '🧘 Yoga',
  [WorkoutType.HIIT]: '⏱️ HIIT',
  [WorkoutType.SPORT]: '⚽️ Sport',
  [WorkoutType.MAX]: '🔝 Max',
};

export const getWorkoutTypeLabel = (type: WorkoutType, hasNewLine?: boolean) => {
  const label = FRIENDLY_WORKOUT_TYPES[type];
  return hasNewLine ? label.replace(' ', '\n') : label;
};

const WORKOUT_TYPE_ICONS = {
  [WorkoutType.STRENGTH]: 'weight-lifter',
  [WorkoutType.CARDIO]: 'run',
  [WorkoutType.MOBILITY]: 'yoga',
  [WorkoutType.WEIGHTS]: 'weight-lifter',
  [WorkoutType.RUN]: 'run',
  [WorkoutType.WALK]: 'walk',
  [WorkoutType.CYCLING]: 'bike',
  [WorkoutType.SWIM]: 'swim',
  [WorkoutType.YOGA]: 'yoga',
  [WorkoutType.HIIT]: 'timer-outline',
  [WorkoutType.SPORT]: 'soccer',
  [WorkoutType.MAX]: 'arrow-up-bold-hexagon-outline',
} as const;

export const getWorkoutTypeIcon = (type: WorkoutType) => WORKOUT_TYPE_ICONS[type];
