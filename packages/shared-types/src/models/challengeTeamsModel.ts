import type {ImageUrl, IsoDate, UUIDString} from './shared';
import {
  type ChallengeBase,
  type ChallengeGroupingType,
  type ChallengeParticipantBase,
  type DailyDataValues,
} from './challengeBase';

export type ChallengeTeam = {
  id: UUIDString;
  name: string;
  isHideFromTeamsLeaderboard?: true;
  isPublicTeam?: true;
  teamPicture?: ImageUrl;
  lastMilestoneMultipleNotificationsSent?: number;
};

export type ChallengeTeamWithRankingValues = ChallengeTeam &
  DailyDataValues & {
    rank: number;
  };

export type ChallengeParticipant = ChallengeParticipantBase & {
  teamId?: UUIDString;
};

export type ChallengeParticipantWithRankingValues = ChallengeParticipantBase &
  DailyDataValues & {
    rank: number;
  };

export type DailyDataByParticipants = Record<
  IsoDate,
  {
    participants: Record<UUIDString, DailyDataValues>;
    teams?: Record<UUIDString, DailyDataValues>;
    teamIdsMetDailyGoal?: UUIDString[];
  }
>;

export type ChallengeBaseTeamOrTeams = ChallengeBase & {
  participants: ChallengeParticipant[];
  participantIds: UUIDString[];
  dailyData?: DailyDataByParticipants;
  lastMilestoneMultipleNotificationsSent?: number;
};

export type IndividualChallenge = ChallengeBaseTeamOrTeams & {
  groupingType: ChallengeGroupingType.INDIVIDUAL;
};

export type TeamsChallenge = ChallengeBaseTeamOrTeams & {
  groupingType: ChallengeGroupingType.TEAMS;
  teams: ChallengeTeam[];
};
