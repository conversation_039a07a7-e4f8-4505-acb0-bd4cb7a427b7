import type {RawTimestamp} from '../firebase';
import type {OneOf} from '../utils';
import type {UUIDString} from './shared';

export enum SummaryReportType {
  CHALLENGE = 'CHALLENGE',
  SEVENDAY = 'SEVENDAY',
  OTHER = 'OTHER',
}
const SUPPORTED_REPORT_TYPES = [SummaryReportType.SEVENDAY, SummaryReportType.CHALLENGE] as const;
export type SupportedReportTypes = (typeof SUPPORTED_REPORT_TYPES)[number];

export const isSupportedReportType = (type: unknown): type is SupportedReportTypes =>
  SUPPORTED_REPORT_TYPES.includes(type as SupportedReportTypes);

export const isValidReportType = (type: unknown): type is SummaryReportType =>
  Object.values(SummaryReportType).includes(type as SummaryReportType);

export const getFriendlyReportName = (type: SummaryReportType) => {
  if (type === SummaryReportType.CHALLENGE) {
    return 'Challenge';
  } else if (type === SummaryReportType.SEVENDAY) {
    return '7-day Wellness';
  }
  return 'Other';
};

export type CreateSummaryReportSevenDayOptions = {
  type: SummaryReportType.SEVENDAY;
  userIds?: UUIDString[];
};

export type CreateSummaryReportChallengeOptions = {
  type: SummaryReportType.CHALLENGE;
  challengeId?: UUIDString;
};

export type CreateSummaryReportDefault = {
  type: '';
};

type CreateSummaryReportTypeBase = {
  name?: string;
  creatorOrganizationId: UUIDString;
  creatorUserId: UUIDString;
  endDate: Date;
  startDate: Date;
};

export type CreateSummaryReportType = CreateSummaryReportTypeBase &
  OneOf<
    [
      CreateSummaryReportSevenDayOptions,
      CreateSummaryReportChallengeOptions,
      CreateSummaryReportDefault,
    ]
  >;

export type CreateSummaryReportDefaultType = CreateSummaryReportTypeBase &
  CreateSummaryReportDefault;

export type CreateSummaryReportSevenDayType = CreateSummaryReportTypeBase &
  CreateSummaryReportSevenDayOptions;

export type CreateSummaryReportChallengeType = CreateSummaryReportTypeBase &
  CreateSummaryReportChallengeOptions;

export type CreateSummaryReportValidTypes =
  | CreateSummaryReportSevenDayType
  | CreateSummaryReportChallengeType;

export type SummaryReport = {
  id: UUIDString;
  name?: string;
  creatorOrganizationId: UUIDString;
  creatorUserId: UUIDString;
  endDateTime: RawTimestamp;
  startDateTime: RawTimestamp;
  type: SummaryReportType;
  userIds: UUIDString[];
  userIdsCompleted?: UUIDString[];
  userIdsFailed?: UUIDString[];
  createdDateTime: RawTimestamp;
  finishedDateTime?: RawTimestamp;
  challengeId?: UUIDString;
};

export type SignedReportUrlRequestDTO = {
  summaryReportId: UUIDString;
  userId: UUIDString;
};

export type RecreateSummaryReportRequestDTO = {
  summaryReportId: UUIDString;
  userId: UUIDString;
};
