import type {IsoDate} from '@types';
import {addDaysIsoDate} from './isoDateMathOnly';

export const getMonthKey = (isoDate: IsoDate) => isoDate.slice(0, 7);

export const getStartOfMonthIsoDate = (date: IsoDate) => {
  const [year, month] = date.split('-').slice(0, 2);
  return `${year}-${month}-01` as IsoDate;
};

// Static lookup for non-February months
const daysInMonth: Record<string, string> = {
  '01': '31',
  '03': '31',
  '04': '30',
  '05': '31',
  '06': '30',
  '07': '31',
  '08': '31',
  '09': '30',
  '10': '31',
  '11': '30',
  '12': '31',
};

export const getEndOfMonthIsoDate = (isoDate: IsoDate) => {
  const [year, month] = isoDate.split('-') as [string, string];
  const yearNumber = Number(year);

  // Handle February separate
  if (month === '02') {
    const isLeap = (yearNumber % 4 === 0 && yearNumber % 100 !== 0) || yearNumber % 400 === 0;
    return `${year}-02-${isLeap ? '29' : '28'}` as IsoDate;
  }

  return `${year}-${month}-${daysInMonth[month]}` as IsoDate;
};

export const getMonthFromIsoDate = (date: IsoDate): IsoDate[] => {
  const startOfMonth = getStartOfMonthIsoDate(date);
  const endOfMonth = getEndOfMonthIsoDate(date);

  const dates: IsoDate[] = [];
  let currentDate = startOfMonth;

  // eslint-disable-next-line no-loops/no-loops -- to loop through the month
  while (currentDate <= endOfMonth) {
    dates.push(currentDate);
    currentDate = addDaysIsoDate(currentDate, 1); // Move to the next day
  }

  return dates;
};
