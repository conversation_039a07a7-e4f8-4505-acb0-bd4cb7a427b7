import {
  convertDateToEndOfDay,
  convertDateToStartOfDay,
  getCanonicalTimezone,
  getIsoTimeFromDate,
  getTimezoneOffsetDateFns,
  getTimezoneOffsetMs,
  getTimezoneOffsetMsLuxon,
  getTimezoneOffsetMsStatic,
  type TimeZones,
} from './dates';
import {isDST} from './datesMathOnly';

describe('getCanonicalTimezone', () => {
  it('should convert alias timezone "US/Eastern" to canonical counterpart "America/New_York"', () => {
    const input = 'US/Eastern';
    const expected = 'America/New_York';

    const result = getCanonicalTimezone(input);

    expect(result).toBe(expected);
  });

  it('should keep timezone the same for "America/New_York"', () => {
    const input = 'America/New_York';
    const expected = 'America/New_York';

    const result = getCanonicalTimezone(input);

    expect(result).toBe(expected);
  });

  it('should convert alias timezone "US/Central" to canonical counterpart "America/Chicago"', () => {
    const input = 'US/Central';
    const expected = 'America/Chicago';

    const result = getCanonicalTimezone(input);

    expect(result).toBe(expected);
  });

  it('should keep timezone the same for "America/Chicago"', () => {
    const input = 'America/Chicago';
    const expected = 'America/Chicago';

    const result = getCanonicalTimezone(input);

    expect(result).toBe(expected);
  });

  it('should convert alias timezone "US/Mountain" to canonical counterpart "America/Denver"', () => {
    const input = 'US/Mountain';
    const expected = 'America/Denver';

    const result = getCanonicalTimezone(input);

    expect(result).toBe(expected);
  });

  it('should convert alias timezone "America/Shiprock" to canonical counterpart "America/Denver"', () => {
    const input = 'America/Shiprock';
    const expected = 'America/Denver';

    const result = getCanonicalTimezone(input);

    expect(result).toBe(expected);
  });

  it('should convert alias timezone "Navajo" to canonical counterpart "America/Denver"', () => {
    const input = 'Navajo';
    const expected = 'America/Denver';

    const result = getCanonicalTimezone(input);

    expect(result).toBe(expected);
  });

  it('should keep timezone the same for "America/Denver"', () => {
    const input = 'America/Denver';
    const expected = 'America/Denver';

    const result = getCanonicalTimezone(input);

    expect(result).toBe(expected);
  });

  it('should keep timezone the same for "America/Phoenix"', () => {
    const input = 'America/Phoenix';
    const expected = 'America/Phoenix';

    const result = getCanonicalTimezone(input);

    expect(result).toBe(expected);
  });

  it('should convert alias timezone "US/Pacific" to canonical counterpart "America/Los_Angeles"', () => {
    const input = 'US/Pacific';
    const expected = 'America/Los_Angeles';

    const result = getCanonicalTimezone(input);

    expect(result).toBe(expected);
  });

  it('should keep timezone the same for "America/Los_Angeles"', () => {
    const input = 'America/Los_Angeles';
    const expected = 'America/Los_Angeles';

    const result = getCanonicalTimezone(input);

    expect(result).toBe(expected);
  });

  it('should throw an error for an invalid timezone "Invalid/Zone"', () => {
    const input = 'Invalid/Zone';

    expect(() => getCanonicalTimezone(input)).toThrow(`Invalid timezone: ${input}`);
  });
});

describe('convertDateToStartOfDay', () => {
  const timeZone = 'America/Los_Angeles';
  it('should set the time to local midnight for the same day (PST)', () => {
    const date = new Date('2025-02-03T10:15:30.000Z');

    const actual = convertDateToStartOfDay(date, timeZone);
    const expected = new Date('2025-02-03T08:00:00.000Z');

    expect(actual.toISOString()).toBe(expected.toISOString());
  });

  it('should set the time to local midnight for the same day starting at end of day', () => {
    const date = new Date('2025-02-04T07:59:59.000Z');

    const actual = convertDateToStartOfDay(date, timeZone);
    const expected = new Date('2025-02-03T08:00:00.000Z');

    expect(actual.toISOString()).toBe(expected.toISOString());
  });

  it('should not change the local date if already at midnight in PST', () => {
    const date = new Date('2025-02-03T08:00:00.000Z');

    const actual = convertDateToStartOfDay(date, timeZone);
    const expected = new Date('2025-02-03T08:00:00.000Z');

    expect(actual.toISOString()).toBe(expected.toISOString());
  });
});

describe('convertDateToEndOfDay', () => {
  const timeZone = 'America/Los_Angeles';
  it('should set the time to local end-of-day (23:59:59.999) for PST', () => {
    const date = new Date('2025-02-03T10:15:30.000Z');

    const actual = convertDateToEndOfDay(date, timeZone);
    const expected = new Date('2025-02-04T07:59:59.999Z');

    expect(actual.toISOString()).toBe(expected.toISOString());
  });
  it('should set the time to local end-of-day (23:59:59.999) when starting at start of day', () => {
    const date = new Date('2025-02-03T08:00:00.000Z');

    const actual = convertDateToEndOfDay(date, timeZone);
    const expected = new Date('2025-02-04T07:59:59.999Z');

    expect(actual.toISOString()).toBe(expected.toISOString());
  });

  it('should not change the local date if it is already end-of-day in PST', () => {
    const date = new Date('2025-02-04T07:59:59.999Z');

    const actual = convertDateToEndOfDay(date, timeZone);
    const expected = new Date('2025-02-04T07:59:59.999Z');

    expect(actual.toISOString()).toBe(expected.toISOString());
  });
});

describe('getIsoTimeFromDate', () => {
  it('should return time for UTC timezone', () => {
    const date = new Date('2021-01-01T12:34:56.789Z');
    const timeZone = 'UTC';
    const actual = getIsoTimeFromDate(date, timeZone);
    const expected = '12:34:56.789';
    expect(actual).toBe(expected);
  });

  it('should return time for America/New_York during standard time', () => {
    const date = new Date('2021-01-01T12:34:56.789Z');
    const timeZone = 'America/New_York';
    // In standard time New York is UTC-5:
    // 12:34:56.789Z -> 07:34:56.789 local time.
    const actual = getIsoTimeFromDate(date, timeZone);
    const expected = '07:34:56.789';
    expect(actual).toBe(expected);
  });

  // DST start tests (spring forward)
  // it('should return time right before DST starts in America/New_York', () => {
  //   // In America/New_York, DST begins on March 14, 2021.
  //   // Before DST starts (UTC-5) the local time corresponding to 2021-03-14T06:59:59.000Z is 01:59:59.000.
  //   const date = new Date('2021-03-14T05:59:59.000Z');
  //   const timeZone = 'America/New_York';
  //   const actual = getIsoTimeFromDate(date, timeZone);
  //   const expected = '00:59:59.000';
  //   expect(actual).toBe(expected);
  // });

  it('should return time right after DST starts in America/New_York', () => {
    // When DST starts, clocks jump from 01:59:59 to 03:00:00.
    // 2021-03-14T07:00:00.000Z in New York (now UTC-4) becomes 03:00:00.000.
    const date = new Date('2021-03-14T07:00:00.000Z');
    const timeZone = 'America/New_York';
    const actual = getIsoTimeFromDate(date, timeZone);
    const expected = '03:00:00.000';
    expect(actual).toBe(expected);
  });

  // DST end tests (fall back)
  // it('should return time right before DST ends in America/New_York', () => {
  //   // In America/New_York, DST ends on November 7, 2021.
  //   // Just before the fall back (still DST, UTC-4), 2021-11-07T05:59:59.000Z corresponds to 01:59:59.000.
  //   const date = new Date('2021-11-07T05:59:59.000Z');
  //   const timeZone = 'America/New_York';
  //   const actual = getIsoTimeFromDate(date, timeZone);
  //   const expected = '01:59:59.000';
  //   expect(actual).toBe(expected);
  // });

  it('should return time right after DST ends in America/New_York', () => {
    // After the DST end, the clock falls back so that 2021-11-07T06:00:00.000Z (UTC-5) becomes 01:00:00.000 local time.
    const date = new Date('2021-11-07T06:00:00.000Z');
    const timeZone = 'America/New_York';
    const actual = getIsoTimeFromDate(date, timeZone);
    const expected = '01:00:00.000';
    expect(actual).toBe(expected);
  });
});

describe('getTimezoneOffsetMs', () => {
  type TestCase = {label: string; date: Date; expected: number};

  const testCases: Partial<Record<TimeZones, TestCase[]>> = {
    'America/New_York': [
      {
        label: 'leap year standard date (2020-02-29)',
        date: new Date('2020-02-29T12:00:00Z'),
        expected: -18_000_000, // UTC-5
      },
      {
        label: 'standard date in December (2024-12-01)',
        date: new Date('2024-12-01T12:00:00Z'),
        expected: -18_000_000, // UTC-5
      },
      {
        label: 'DST date in July (2024-07-01)',
        date: new Date('2024-07-01T12:00:00Z'),
        expected: -14_400_000, // UTC-4
      },
      // Corrected DST boundaries:
      {
        label: 'the moment before DST leaps forward (2024-03-10T06:59:59.999Z)',
        date: new Date('2024-03-10T06:59:59.999Z'),
        expected: -18_000_000, // UTC-5
      },
      {
        label: 'the moment after DST leaps forward (2024-03-10T07:00:00.000Z)',
        date: new Date('2024-03-10T07:00:00.000Z'),
        expected: -14_400_000, // UTC-4
      },
      {
        label: 'the moment before DST undoes (2024-11-03T05:59:59.999Z)',
        date: new Date('2024-11-03T05:59:59.999Z'),
        expected: -14_400_000, // UTC-4
      },
      {
        label: 'the moment after DST undoes (2024-11-03T06:00:00.000Z)',
        date: new Date('2024-11-03T06:00:00.000Z'),
        expected: -18_000_000, // UTC-5
      },
    ],
    'America/Chicago': [
      {
        label: 'leap year standard date (2020-02-29)',
        date: new Date('2020-02-29T12:00:00Z'),
        expected: -21_600_000, // UTC-6
      },
      {
        label: 'standard date in December (2024-12-01)',
        date: new Date('2024-12-01T12:00:00Z'),
        expected: -21_600_000, // UTC-6
      },
      {
        label: 'DST date in July (2024-07-01)',
        date: new Date('2024-07-01T12:00:00Z'),
        expected: -18_000_000, // UTC-5
      },
      // Corrected DST boundaries:
      {
        label: 'the moment before DST leaps forward (2024-03-10T07:59:59.999Z)',
        date: new Date('2024-03-10T07:59:59.999Z'),
        expected: -21_600_000, // UTC-6
      },
      {
        label: 'the moment after DST leaps forward (2024-03-10T08:00:00.000Z)',
        date: new Date('2024-03-10T08:00:00.000Z'),
        expected: -18_000_000, // UTC-5
      },
      {
        label: 'the moment before DST undoes (2024-11-03T06:59:59.999Z)',
        date: new Date('2024-11-03T06:59:59.999Z'),
        expected: -18_000_000, // UTC-5
      },
      {
        label: 'the moment after DST undoes (2024-11-03T07:00:00.000Z)',
        date: new Date('2024-11-03T07:00:00.000Z'),
        expected: -21_600_000, // UTC-6
      },
    ],
    'America/Los_Angeles': [
      {
        label: 'leap year standard date (2020-02-29)',
        date: new Date('2020-02-29T12:00:00Z'),
        expected: -28_800_000, // UTC-8
      },
      {
        label: 'standard date in December (2024-12-01)',
        date: new Date('2024-12-01T12:00:00Z'),
        expected: -28_800_000, // UTC-8
      },
      {
        label: 'DST date in July (2024-07-01)',
        date: new Date('2024-07-01T12:00:00Z'),
        expected: -25_200_000, // UTC-7
      },
      // Corrected DST boundaries:
      {
        label: 'the moment before DST leaps forward (2024-03-10T09:59:59.999Z)',
        date: new Date('2024-03-10T09:59:59.999Z'),
        expected: -28_800_000, // UTC-8
      },
      {
        label: 'the moment after DST leaps forward (2024-03-10T10:00:00.000Z)',
        date: new Date('2024-03-10T10:00:00.000Z'),
        expected: -25_200_000, // UTC-7
      },
      {
        label: 'the moment before DST undoes (2024-11-03T08:59:59.999Z)',
        date: new Date('2024-11-03T08:59:59.999Z'),
        expected: -25_200_000, // UTC-7
      },
      {
        label: 'the moment after DST undoes (2024-11-03T09:00:00.000Z)',
        date: new Date('2024-11-03T09:00:00.000Z'),
        expected: -28_800_000, // UTC-8
      },
    ],
  };

  Object.entries(testCases).forEach(([timeZone, cases]) => {
    describe(timeZone, () => {
      cases.forEach(({date, expected, label}) => {
        it(`should return ${expected} for ${label}`, () => {
          expect(getTimezoneOffsetMs(timeZone as TimeZones, date)).toBe(expected);
        });
      });
    });
  });
});

describe('isDST', () => {
  // Always DST for any date between April (4) and October (10)
  it('should return true for a mid-summer date (e.g., July 1, 2024 at noon)', () => {
    expect(isDST(2024, 7, 1, 12, 0)).toBe(true);
  });

  it('should return true for an April date (e.g., April 15, 2024 at noon)', () => {
    expect(isDST(2024, 4, 15, 12, 0)).toBe(true);
  });

  // Always NOT DST for months 1, 2, 12
  it('should return false for a January date (e.g., January 10, 2024 at noon)', () => {
    expect(isDST(2024, 1, 10, 12, 0)).toBe(false);
  });

  it('should return false for a December date (e.g., December 31, 2024 at noon)', () => {
    expect(isDST(2024, 12, 31, 12, 0)).toBe(false);
  });

  // Check March boundary (second Sunday is when DST starts)
  // For 2024, second Sunday in March is March 10.
  it('should return false for early March (e.g., March 1, 2024 at noon)', () => {
    expect(isDST(2024, 3, 1, 12, 0)).toBe(false);
  });

  it('should return false for a day just before the second Sunday in March (March 9, 2024 at noon)', () => {
    expect(isDST(2024, 3, 9, 12, 0)).toBe(false);
  });

  // For March 10, 2024: DST starts at 2:00 AM local time.
  // At exactly 2:00, DST becomes active.
  it('should return true for the exact second Sunday in March (March 10, 2024 at 2:00 AM)', () => {
    expect(isDST(2024, 3, 10, 2, 0)).toBe(true);
  });

  it('should return true for a day after the second Sunday in March (March 11, 2024 at noon)', () => {
    expect(isDST(2024, 3, 11, 12, 0)).toBe(true);
  });

  // Check November boundary (first Sunday is when DST ends)
  // For 2024, first Sunday in November is November 3.
  // DST ends at 2:00 AM, so before 2:00 it is still DST, at/after 2:00 it's not.
  it('should return true for a day just before the first Sunday in November (November 2, 2024 at noon)', () => {
    expect(isDST(2024, 11, 2, 12, 0)).toBe(true);
  });

  // Testing the boundary on November 3, 2024:
  // If we check at 2:00 AM or later, DST is off.
  it('should return false for the exact first Sunday in November (November 3, 2024 at 2:00 AM)', () => {
    expect(isDST(2024, 11, 3, 2, 0)).toBe(false);
  });

  it('should return false for a day after the first Sunday in November (November 4, 2024 at noon)', () => {
    expect(isDST(2024, 11, 4, 12, 0)).toBe(false);
  });

  // 2023 edges
  // For 2023, the second Sunday in March is March 12.
  it('should return false for the day before the second Sunday in March (March 11, 2023 at noon)', () => {
    expect(isDST(2023, 3, 11, 12, 0)).toBe(false);
  });

  it('should return true for the second Sunday in March (March 12, 2023 at 2:00 AM)', () => {
    expect(isDST(2023, 3, 12, 2, 0)).toBe(true);
  });

  // For 2023, the first Sunday in November is November 5.
  it('should return true for the day before the first Sunday in November (November 4, 2023 at noon)', () => {
    expect(isDST(2023, 11, 4, 12, 0)).toBe(true);
  });

  it('should return false for the first Sunday in November (November 5, 2023 at 2:00 AM)', () => {
    expect(isDST(2023, 11, 5, 2, 0)).toBe(false);
  });

  // 2025 edges
  // For 2025, the second Sunday in March is March 9.
  it('should return false for the day before the second Sunday in March (March 8, 2025 at noon)', () => {
    expect(isDST(2025, 3, 8, 12, 0)).toBe(false);
  });

  it('should return true for the second Sunday in March (March 9, 2025 at 2:00 AM)', () => {
    expect(isDST(2025, 3, 9, 2, 0)).toBe(true);
  });

  // For 2025, the first Sunday in November is November 2.
  it('should return true for the day before the first Sunday in November (November 1, 2025 at noon)', () => {
    expect(isDST(2025, 11, 1, 12, 0)).toBe(true);
  });

  it('should return false for the first Sunday in November (November 2, 2025 at 2:00 AM)', () => {
    expect(isDST(2025, 11, 2, 2, 0)).toBe(false);
  });
});

describe('getTimezoneOffsetMs performance', () => {
  const timeZone = 'America/Los_Angeles';

  it('should run getTimezoneOffsetMsStatic in under 500 ms', () => {
    const date = new Date('2025-02-03T10:15:30.000Z');

    const timeStart = Date.now();
    // For 1M times, get the timezone offset for a new date
    Array.from({length: 100_000})
      .fill(0)
      .forEach(() => new Date(date.getTime() + getTimezoneOffsetMsStatic(timeZone, date)));
    const timeEnd = Date.now();

    const actualElapsed = timeEnd - timeStart;
    expect(actualElapsed).toBeLessThanOrEqual(500);
  });

  it('should run getTimezoneOffsetDateFns in under 3 second', () => {
    const date = new Date('2025-02-03T10:15:30.000Z');

    const timeStart = Date.now();
    // For 1M times, get the timezone offset for a new date
    Array.from({length: 100_000})
      .fill(0)
      .forEach(() => new Date(date.getTime() + getTimezoneOffsetDateFns(timeZone, date)));
    const timeEnd = Date.now();

    const actualElapsed = timeEnd - timeStart;
    expect(actualElapsed).toBeLessThanOrEqual(3000);
  });

  it('should run getTimezoneOffsetMsLuxon in under 5 second', () => {
    const date = new Date('2025-02-03T10:15:30.000Z');

    const timeStart = Date.now();
    // For 1M times, get the timezone offset for a new date
    Array.from({length: 100_000})
      .fill(0)
      .forEach(() => new Date(date.getTime() + getTimezoneOffsetMsLuxon(timeZone, date)));
    const timeEnd = Date.now();

    const actualElapsed = timeEnd - timeStart;
    expect(actualElapsed).toBeLessThanOrEqual(5000);
  });
});

// describe('zoned time performance', () => {
//   const timeZone = 'America/Los_Angeles';
//   it('should run toZonedTime in under 100ms', () => {
//     const date = new Date('2025-02-03T10:15:30.000Z');

//     const timeStart = Date.now();
//     // For 1M times, get the timezone offset for a new date
//     Array.from({length: 1_000_000})
//       .fill(0)
//       .forEach(() => toZonedTime(date, timeZone));
//     const timeEnd = Date.now();

//     const actualElapsed = timeEnd - timeStart;
//     expect(actualElapsed).toBeLessThanOrEqual(1);
//   });

//   it('should run toZonedTime2 in under 1 second', () => {
//     const date = new Date('2025-02-03T10:15:30.000Z');

//     const timeStart = Date.now();
//     // For 1M times, get the timezone offset for a new date
//     Array.from({length: 1_000_000})
//       .fill(0)
//       .forEach(() => toZonedTimeDateFns(date, timeZone));
//     const timeEnd = Date.now();

//     const actualElapsed = timeEnd - timeStart;
//     expect(actualElapsed).toBeLessThanOrEqual(1);
//   });

//   it('should run fromZonedTime in under 1 second', () => {
//     const date = new Date('2025-02-03T10:15:30.000Z');

//     const timeStart = Date.now();
//     // For 1M times, get the timezone offset for a new date
//     Array.from({length: 1_000_000})
//       .fill(0)
//       .forEach(() => fromZonedTime(date, timeZone));
//     const timeEnd = Date.now();

//     const actualElapsed = timeEnd - timeStart;
//     expect(actualElapsed).toBeLessThanOrEqual(1);
//   });

//   it('should run fromZonedTime2 in under 1 second', () => {
//     const date = new Date('2025-02-03T10:15:30.000Z');

//     const timeStart = Date.now();
//     // For 1M times, get the timezone offset for a new date
//     Array.from({length: 1_000_000})
//       .fill(0)
//       .forEach(() => fromZonedTimeDateFns(date, timeZone));
//     const timeEnd = Date.now();

//     const actualElapsed = timeEnd - timeStart;
//     expect(actualElapsed).toBeLessThanOrEqual(1);
//   });
// });
