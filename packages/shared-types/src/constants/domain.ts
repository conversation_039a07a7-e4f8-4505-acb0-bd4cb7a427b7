import type {TimeZones, UUIDString} from '@types';
import {SECONDS_1_HOUR, SECONDS_24_HOURS} from './dateAndTimeConstants';

// eslint-disable-next-line @typescript-eslint/naming-convention, max-lines-per-function -- constants
export const DOMAIN_CONSTANTS = () =>
  ({
    SEARCH: {
      MIN_SEARCH_LENGTH: 1,
      MAX_DISPLAY_RESULTS: 5,
      MAX_USER_SEARCH_RESULTS: 10,
    },
    DEBOUNCE: {
      DEFAULT_COOLDOWN_MS: 2000,
      SYNC_HEALTH_STATS_MS: 5000,
      ACTION_SHEET_OPEN_MS: 2000,
      SYNC_ANALYTICS: 5000,
    },
    BACKGROUND_FETCH: {
      MINIMUM_INTERVAL_IN_SECONDS: 60 * 60,
    },
    SHARE_WORKOUT_LOAD_DELAY_MS: 2000,
    RECEIVE_INVITE_CODE_DELAY_MS: 2000,
    DEFAULT_ASYNC_TIMEOUT_MS: 30 * 1000, // 30 seconds
    DEFAULT_STEP_LENGTH_INCHES: 29,
    GOALS: {
      DEFAULT_GOAL_MILEAGE_IN_METERS: 5149.9008,
      DEFAULT_GOAL_MILEAGE_IN_MILES: 3.2,
      DEFAULT_GOAL_STEP_COUNT: 7000,
      // DEFAULT_GOAL_MILEAGE_IN_METERS: 8046.72, // 5 miles
      // DEFAULT_GOAL_MILEAGE_IN_MILES: 5,
      // DEFAULT_GOAL_STEP_COUNT: 10_000,
      GOAL_MILEAGE_INTERVAL: 0.1,
      GOAL_STEP_COUNT_INTERVAL: 100,
      MIN_GOAL_MILEAGE_IN_MILES: 1,
      MAX_GOAL_MILEAGE_IN_MILES: 10,
      MIN_GOAL_STEP_COUNT: 2000,
      MAX_GOAL_STEP_COUNT: 20_000,
    },
    NOTIFICATIONS: {
      DEFAULT_WELLNESS_BLOG_OFFSET_IN_SECONDS: 9 * SECONDS_1_HOUR, // 9:00 AM
      DEFAULT_WELLNESS_QUIZ_NOTIFICATION_OFFSET_IN_SECONDS: 9 * SECONDS_1_HOUR, // 9:00 AM
      DEFAULT_WELLNESS_BLOG_INTERVAL_IN_DAYS: 3,
      DEFAULT_WELLNESS_BLOG_INTERVAL_IN_SECONDS: 3 * SECONDS_24_HOURS,
      MIN_INTERVAL_IN_DAYS: 1,
      MAX_INTERVAL_IN_DAYS: 14,
      INTERVAL_STEP_IN_DAYS: 1,
      DELAY_BEFORE_NOTIFICATION_MODAL_MS: 5000,
      APP_OPENS_BEFORE_NOTIFICATION_MODAL: 2,
    },
    STREAKS: {
      MIN_MOVEMENT_STREAK_LENGTH: 1,
      MIN_WELLNESS_QUIZ_STREAK_LENGTH: 1,
    },
    CLOUD_STORAGE_PATH_PREFIXES: {
      WORKOUTS: (workoutId: UUIDString) => `workouts/${workoutId}/`,
      APP_USERS_PROFILE: (userId: UUIDString) => `appUsers/${userId}/`,
      CHALLENGE: (challengeId: UUIDString, postId: UUIDString) =>
        `challenges/${challengeId}/posts/${postId}/`,
      CHALLENGE_TEAM_PHOTO: (challengeId: UUIDString, teamId: UUIDString | null) =>
        `challenge/${challengeId}/teams/${teamId}/`,
      CHALLENGE_GROUP_PHOTO: (challengeId: UUIDString, groupId: UUIDString | null) =>
        `challenge/${challengeId}/challengeGroups/${groupId ?? 'global'}/`,
      CHALLENGE_BANNER: (challengeId: UUIDString) => `challenges/${challengeId}/banner/`,
      SUMMARY_REPORTS: (summaryReportId: UUIDString) => `reports/${summaryReportId}/`,
      MEALS: (mealId: UUIDString) => `meals/${mealId}/`,
      ORGANIZATION_POST: (orgId: UUIDString, postId: UUIDString) =>
        `organizations/${orgId}/posts/${postId}/`,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any -- can be any
    } satisfies Record<string, (...arg: any[]) => `${string}/`>,
    SNACK_DEFAULT_DELAY_MS: 4000,
    CHALLENGE: {
      POPUP_STEP_THRESHOLD: 20_000,
      POPUP_DISTANCE_THRESHOLD_IN_METERS: 16_093.4,
      GROUP_AGGREGATION_BATCH_DEFAULTS: {
        BATCH_INTERVAL_MINUTES: 15,
      },
    },
    ANALYTICS: {
      BATCH_DELAY_MS: 10_000,
    },
    CALENDAR: {
      DAYS_BEFORE_SCROLL: 30,
      DAYS_AFTER_SCROLL: 5,
    },
    SCREEN: {
      MAX_WIDTH: 600,
    },
    FONT: {
      BOLD_FAMILY: 'Epilogue-Bold',
      REGULAR_FAMILY: 'Epilogue-Regular',
      SEMI_BOLD_FAMILY: 'Epilogue-SemiBold',
      MEDIUM_FAMILY: 'Epilogue-Medium',
      // BOLD_FAMILY: Platform.select({
      //   android: 'Epilogue_700Bold',
      //   ios: 'Epilogue-Bold',
      // }) as string,
      // REGULAR_FAMILY: Platform.select({
      //   android: 'Epilogue_400Regular',
      //   ios: 'Epilogue-Regular',
      // }) as string,
    },
    SPECIAL_USERS: {
      USER_EMAILS: ['<EMAIL>', '<EMAIL>'] as string[],
      FLY_BODIES_CO_USERS: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ] as string[],
      USER_PASSWORD: '92f873d2-2b5a-470e-9451-4f45ecd977c1' as string,
      DEBUG_USERS: ['<EMAIL>'] as string[],
      CLEAR_TRACKING_DEVICE_SETTINGS_USERS: [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ] as string[],
    },
    MEASUREMENTS: {
      INCHES_IN_METER: 39.37,
    },
    POSTS: {
      MAX_IMAGE_HEIGHT: 300,
      FEED_LOAD_LIMIT: 5,
      FEED_LOAD_LIMIT_INCREMENT: 5,
    },
    MEAL: {
      MAX_IMAGE_HEIGHT: 300,
    },
    DEFAULT_TIME_ZONE: 'America/Los_Angeles' as TimeZones,
  }) as const;
