import type {HealthKitPermissions, HealthStatusResult} from 'react-native-health';
import {HealthConnectPermissionEnum, HealthPermission, HealthStatusCode} from '@types';
import type {Permission as HealthConnectPermission} from 'react-native-health-connect/lib/typescript/types';

export type HealthConnectPermissionSet = {
  expectedGrantedPermissions: HealthConnectPermissionEnum[];
  permissions: HealthConnectPermission[];
};

export const ALL_PERMISSION_HEALTH_CONNECT: HealthConnectPermissionSet = {
  expectedGrantedPermissions: [
    HealthConnectPermissionEnum.STEPS,
    HealthConnectPermissionEnum.STEPS,
    HealthConnectPermissionEnum.DISTANCE,
    HealthConnectPermissionEnum.DISTANCE,
    HealthConnectPermissionEnum.WEIGHT,
    HealthConnectPermissionEnum.WEIGHT,
  ],
  permissions: [
    {accessType: 'read', recordType: HealthConnectPermissionEnum.STEPS},
    {accessType: 'write', recordType: HealthConnectPermissionEnum.STEPS},
    {accessType: 'read', recordType: HealthConnectPermissionEnum.DISTANCE},
    {accessType: 'write', recordType: HealthConnectPermissionEnum.DISTANCE},
    {accessType: 'read', recordType: HealthConnectPermissionEnum.WEIGHT},
    {accessType: 'write', recordType: HealthConnectPermissionEnum.WEIGHT},
  ],
};

export const STEP_PERMISSIONS_HEALTH_CONNECT: HealthConnectPermissionSet = {
  expectedGrantedPermissions: [
    HealthConnectPermissionEnum.STEPS,
    HealthConnectPermissionEnum.STEPS,
    HealthConnectPermissionEnum.DISTANCE,
    HealthConnectPermissionEnum.DISTANCE,
  ],
  permissions: [
    {accessType: 'read', recordType: HealthConnectPermissionEnum.STEPS},
    {accessType: 'write', recordType: HealthConnectPermissionEnum.STEPS},
    {accessType: 'read', recordType: HealthConnectPermissionEnum.DISTANCE},
    {accessType: 'write', recordType: HealthConnectPermissionEnum.DISTANCE},
  ],
};

export const WEIGHT_PERMISSIONS_HEALTH_CONNECT: HealthConnectPermissionSet = {
  expectedGrantedPermissions: [
    HealthConnectPermissionEnum.WEIGHT,
    HealthConnectPermissionEnum.WEIGHT,
  ],
  permissions: [
    {accessType: 'read', recordType: HealthConnectPermissionEnum.WEIGHT},
    {accessType: 'write', recordType: HealthConnectPermissionEnum.WEIGHT},
  ],
};

export type HealthKitPermissionsAndResult = {
  permissions: HealthKitPermissions;
  result: HealthStatusResult;
};

export const ALL_PERMISSIONS_HEALTH_KIT: HealthKitPermissionsAndResult = {
  permissions: {
    permissions: {
      read: [
        HealthPermission.DistanceWalkingRunning,
        HealthPermission.Steps,
        HealthPermission.Weight,
      ],
      write: [
        HealthPermission.DistanceWalkingRunning,
        HealthPermission.Steps,
        HealthPermission.Weight,
      ],
    },
  },
  result: {
    permissions: {
      read: [
        HealthStatusCode.SharingAuthorized,
        HealthStatusCode.SharingAuthorized,
        HealthStatusCode.SharingAuthorized,
      ],
      write: [
        HealthStatusCode.SharingAuthorized,
        HealthStatusCode.SharingAuthorized,
        HealthStatusCode.SharingAuthorized,
      ],
    },
  },
};

export const STEP_PERMISSIONS_HEALTH_KIT: HealthKitPermissionsAndResult = {
  permissions: {
    permissions: {
      read: [HealthPermission.Steps, HealthPermission.DistanceWalkingRunning],
      write: [HealthPermission.Steps, HealthPermission.DistanceWalkingRunning],
    },
  },
  result: {
    permissions: {
      read: [HealthStatusCode.SharingAuthorized, HealthStatusCode.SharingAuthorized],
      write: [HealthStatusCode.SharingAuthorized, HealthStatusCode.SharingAuthorized],
    },
  },
};

export const WEIGHT_PERMISSIONS_HEALTH_KIT: HealthKitPermissionsAndResult = {
  permissions: {
    permissions: {
      read: [HealthPermission.Weight],
      write: [HealthPermission.Weight],
    },
  },
  result: {
    permissions: {
      read: [HealthStatusCode.SharingAuthorized],
      write: [HealthStatusCode.SharingAuthorized],
    },
  },
};
