import * as Application from 'expo-application';
import * as Constants from 'expo-constants';
import * as Device from 'expo-device';
import {Platform} from 'react-native';
import {APP_ENV} from '@env';
import type {AppConfig} from '@types';
import localAppConfigDev from '../../data/config/app-config-dev.json';
import localAppConfigProd from '../../data/config/app-config-prod.json';

// Expo
export {nativeApplicationVersion as nativeAppVersion, nativeBuildVersion} from 'expo-application';

// Platform
export const isExpoGo =
  Constants.default.executionEnvironment === Constants.ExecutionEnvironment.StoreClient;
export const isAndroid = Platform.OS === 'android';
export const isIos = Platform.OS === 'ios';
export const isWeb = Platform.OS === 'web';
export const isTestEnvironment = process.env.NODE_ENV === 'test';
export const isSimulator = !Device.isDevice;
export const isDevelopmentClient =
  Constants.default.executionEnvironment !== Constants.ExecutionEnvironment.Standalone;

// Project
export const APP_NAME = Application.applicationName;
// @ts-ignore exists according to documentation
// eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access, import-x/namespace -- allowed
export const projectId: string = Constants.expoConfig?.extra?.eas?.projectId ?? '8e1d0d69-c407-45e8-a415-f45488ac6a68';

// Environment
export const isDev = APP_ENV === 'development';
export const isProd = APP_ENV === 'production';

// Misc
export const BASE_URL_EXPO_NOTIFICATION_API = 'https://exp.host/--/api/v2';
export const STATIC_APP_CONFIG: AppConfig = isProd ? localAppConfigProd : localAppConfigDev;
