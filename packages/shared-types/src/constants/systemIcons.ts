export type SymbolIcon = {
  android: string;
  ios: string;
};

//? Android: https://fonts.google.com/icons
//? iOS: https://github.com/andrewtavis/sf-symbols-online/blob/master/README_dark.md
export const SYSTEM_ICONS = {
  edit: {
    android: 'edit',
    ios: 'pencil',
  },
  editProfile: {
    android: 'edit_square',
    ios: 'square.and.pencil',
  },
  delete: {
    android: 'delete',
    ios: 'trash',
  },
  changeProfile: {
    android: 'swap_horiz',
    ios: 'arrow.left.arrow.right',
  },
  cancel: {
    android: 'cancel',
    ios: 'x.circe',
  },
  sendEmail: {
    android: 'mail',
    ios: 'envelope',
  },
  individualChallenge: {
    android: 'person',
    ios: 'person',
  },
  challengeParticipant: {
    android: 'person',
    ios: 'person',
  },
  challengeCaptain: {
    android: 'shield_person',
    ios: 'person.badge.shield.checkmark',
  },
  challengeOwner: {
    android: 'crown',
    ios: 'crown',
  },
  teamsChallenge: {
    android: 'group',
    ios: 'person.2',
  },
  groupsChallenge: {
    android: 'groups',
    ios: 'person.3',
  },
  challengeLevels: {
    android: 'sort',
    ios: 'list.bullet.indent',
  },
  none: {
    android: 'block-helper',
    ios: 'nosign',
  },
  refresh: {
    android: 'refresh',
    ios: 'arrow.clockwise',
  },
  download: {
    android: 'download',
    ios: 'square.and.arrow.down',
  },
  createForSelf: {
    android: 'add',
    ios: 'plus',
  },
  createForClients: {
    android: 'group-add',
    ios: 'person.3',
  },
  breakfast: {
    android: 'egg_alt',
    ios: 'sunrise.fill',
  },
  lunch: {
    android: 'lunch_dining',
    ios: 'sun.max.fill',
  },
  dinner: {
    android: 'set_meal',
    ios: 'moon.stars.fill',
  },
  snack: {
    android: 'nutrition',
    ios: 'fork.knife',
  },
  message: {
    android: 'chat_bubble',
    ios: 'message',
  },
  broadcastMessage: {
    android: 'broadcast',
    ios: 'person.wave.2',
  },
  stageSelector: {
    android: 'date_range',
    ios: 'calendar.badge.clock',
  },
} as const satisfies Record<string, SymbolIcon>;
