export const STORAGE_KEYS = {
  //! This value is used to store both phone and email credentials. Keep the same name to guarantee
  //! existing users credentials are not lost.
  CREDENTIALS_FOR_SIGN_IN: 'EMAIL_FOR_SIGN_IN',
  NAVIGATION_STATE: 'NAVIGATION_STATE',
  NOTIFICATION_SETTINGS: 'notifications-settings',
  LOCAL_DEVICE_SETTINGS: 'local-device-settings',
  ANDROID_DATA_ORIGINS: 'ANDROID_DATA_ORIGINS_V2',
  ANDROID_SELECTED_DATA_ORIGIN: 'ANDROID_SELECTED_DATA_ORIGIN_V2',
  SELECTED_ORGANIZATION_FEED: 'SELECTED_ORGANIZATION_FEED',
  HOME_CALENDAR_FILTERS: 'HOME_CALENDAR_FILTERS',
  SELECTED_CHALLENGE_STAGE: 'SELECTED_CHALLENGE_STAGE',
  FIREBASE_AUTH: 'firebase:authUser',
  AUTH_CONTEXT: 'AUTH_CONTEXT',
} as const;

type StorageKeyKeyType = keyof typeof STORAGE_KEYS;
export type StorageKeyType = (typeof STORAGE_KEYS)[StorageKeyKeyType];

export const KEYS_TO_REMOVE_ON_RELOAD: StorageKeyType[] = [STORAGE_KEYS.NAVIGATION_STATE];
