import {addDaysIsoDate} from './isoDateMathOnly';
import {getEndOfWeekIsoDate, getStartOfWeekIsoDate, getWeekFromIsoDate} from './isoDateWeekHelpers';

describe('week arithmetic', () => {
  it('should return the start of the previous week', () => {
    const referenceDate = '2024-09-30'; // A Monday
    const actual = addDaysIsoDate(referenceDate, -7);
    expect(actual).toEqual('2024-09-23');
  });

  it('should return the start of the next week', () => {
    const referenceDate = '2024-09-30'; // A Monday
    const actual = addDaysIsoDate(referenceDate, 7);
    expect(actual).toEqual('2024-10-07');
  });
});

describe('week boundary checking', () => {
  it('should return the Monday of the given week', () => {
    const referenceDate = '2024-09-30'; // A Monday
    const actual = getStartOfWeekIsoDate(referenceDate);
    expect(actual).toEqual('2024-09-30');
  });

  it('should handle dates mid-week', () => {
    const referenceDate = '2024-10-02'; // A Wednesday
    const actual = getStartOfWeekIsoDate(referenceDate);
    expect(actual).toEqual('2024-09-30');
  });

  it('should handle year boundary (end of year)', () => {
    const referenceDate = '2023-12-31'; // A Sunday
    const actual = getStartOfWeekIsoDate(referenceDate);
    expect(actual).toEqual('2023-12-25');
  });

  it('should handle year boundary (start of year)', () => {
    const referenceDate = '2024-01-01'; // A Monday
    const actual = getStartOfWeekIsoDate(referenceDate);
    expect(actual).toEqual('2024-01-01');
  });

  it('should return the Sunday of the given week', () => {
    const referenceDate = '2024-09-30'; // A Monday
    const actual = getEndOfWeekIsoDate(referenceDate);
    expect(actual).toEqual('2024-10-06');
  });

  it('should return the end of the week for a date in the middle of the week', () => {
    const referenceDate = '2024-09-25'; // A Wednesday
    const actual = getEndOfWeekIsoDate(referenceDate);
    expect(actual).toEqual('2024-09-29');
  });

  it('should return the correct end of the week for the last day of the year', () => {
    const referenceDate = '2024-12-31'; // A Tuesday
    const actual = getEndOfWeekIsoDate(referenceDate);
    expect(actual).toEqual('2025-01-05');
  });

  it('should return the correct end of the week for the first day of the year', () => {
    const referenceDate = '2024-01-01'; // A Monday
    const actual = getEndOfWeekIsoDate(referenceDate);
    expect(actual).toEqual('2024-01-07');
  });

  it('should return the correct end of the week when the date is a Sunday', () => {
    const referenceDate = '2024-09-29'; // A Sunday
    const actual = getEndOfWeekIsoDate(referenceDate);
    expect(actual).toEqual('2024-09-29');
  });

  it('should handle leap year correctly and return the correct end of the week', () => {
    const referenceDate = '2024-02-28'; // A Wednesday (leap year)
    const actual = getEndOfWeekIsoDate(referenceDate);
    expect(actual).toEqual('2024-03-03');
  });
});

describe('generating a week of dates', () => {
  it('should get the expected 7 week dates given 2024-09-30 as reference date', () => {
    const referenceDate = '2024-09-30';

    const actual = getWeekFromIsoDate(referenceDate);
    const expected = [
      '2024-09-30',
      '2024-10-01',
      '2024-10-02',
      '2024-10-03',
      '2024-10-04',
      '2024-10-05',
      '2024-10-06',
    ];

    expect(actual).toEqual(expected);
  });

  it('should get the expected 7 week dates given 2024-10-06 as reference date', () => {
    const referenceDate = '2024-10-06';

    const actual = getWeekFromIsoDate(referenceDate);
    const expected = [
      '2024-09-30',
      '2024-10-01',
      '2024-10-02',
      '2024-10-03',
      '2024-10-04',
      '2024-10-05',
      '2024-10-06',
    ];

    expect(actual).toEqual(expected);
  });

  it('should get the expected 7 week dates given 2024-10-07 as reference date', () => {
    const referenceDate = '2024-10-07';

    const actual = getWeekFromIsoDate(referenceDate);
    const expected = [
      '2024-10-07',
      '2024-10-08',
      '2024-10-09',
      '2024-10-10',
      '2024-10-11',
      '2024-10-12',
      '2024-10-13',
    ];

    expect(actual).toEqual(expected);
  });
});
