export enum TrackingDeviceTypes {
  Apple = 'Apple',
  Garmin = 'Garmin',
  Samsung = 'Samsung',
  Fitbit = 'Fitbit',
  Whoop = 'Whoop',
  <PERSON><PERSON> = '<PERSON><PERSON>',
  Oura = 'Oura',
  Other = 'Other',
  Phone = 'Phone',
  NoDevice = 'NoDevice',
}

export const allTrackingDeviceTypesIos = [
  TrackingDeviceTypes.Apple,
  TrackingDeviceTypes.Garmin,
  TrackingDeviceTypes.Samsung,
  TrackingDeviceTypes.Fitbit,
  TrackingDeviceTypes.Whoop,
  TrackingDeviceTypes.Coros,
  TrackingDeviceTypes.Oura,
  TrackingDeviceTypes.Other,
  TrackingDeviceTypes.Phone,
  TrackingDeviceTypes.NoDevice,
];

export const allTrackingDeviceTypesAndroid = [
  TrackingDeviceTypes.Garmin,
  TrackingDeviceTypes.Samsung,
  TrackingDeviceTypes.Fitbit,
  TrackingDeviceTypes.Whoop,
  TrackingDeviceTypes.Coros,
  TrackingDeviceTypes.Oura,
  TrackingDeviceTypes.Other,
  TrackingDeviceTypes.NoDevice,
];

const FRIENDLY_MAPPING = {
  [TrackingDeviceTypes.Apple]: 'Apple Watch',
  [TrackingDeviceTypes.Garmin]: 'Garmin',
  [TrackingDeviceTypes.Samsung]: 'Samsung Galaxy',
  [TrackingDeviceTypes.Fitbit]: 'Fitbit',
  [TrackingDeviceTypes.Whoop]: 'WHOOP',
  [TrackingDeviceTypes.Coros]: 'Coros',
  [TrackingDeviceTypes.Oura]: 'Oura Ring',
  [TrackingDeviceTypes.Other]: 'Device not listed',
  [TrackingDeviceTypes.Phone]: 'Phone',
  [TrackingDeviceTypes.NoDevice]: "I don't have a device/wearable",
} satisfies Record<TrackingDeviceTypes, string>;

export const getTrackingDeviceTypeDisplayName = (type: TrackingDeviceTypes | undefined) =>
  type ? FRIENDLY_MAPPING[type] : 'Not set';

export type WearableConnectionRequestDTO = {
  wearableDeviceBrand: TrackingDeviceTypes;
};

export type TrackingDeviceConnectionRequestDTO = {
  trackingDeviceType: TrackingDeviceTypes;
};

const PRIMARY_TRACKING_DEVICE_TYPES = new Set([
  TrackingDeviceTypes.Apple,
  TrackingDeviceTypes.Garmin,
  TrackingDeviceTypes.Fitbit,
]);

export const isPrimaryTrackingDeviceType = (
  type: TrackingDeviceTypes | undefined,
): type is TrackingDeviceTypes.Apple | TrackingDeviceTypes.Garmin | TrackingDeviceTypes.Fitbit =>
  !!type && PRIMARY_TRACKING_DEVICE_TYPES.has(type);

const SECONDARY_TRACKING_DEVICE_TYPES = new Set([
  TrackingDeviceTypes.Samsung,
  TrackingDeviceTypes.Whoop,
  TrackingDeviceTypes.Coros,
  TrackingDeviceTypes.Oura,
  TrackingDeviceTypes.Other,
]);

export const isSecondaryTrackingDeviceType = (
  type: TrackingDeviceTypes | undefined,
): type is
| TrackingDeviceTypes.Samsung
| TrackingDeviceTypes.Whoop
| TrackingDeviceTypes.Coros
| TrackingDeviceTypes.Oura
| TrackingDeviceTypes.Other => !!type && SECONDARY_TRACKING_DEVICE_TYPES.has(type);

export enum TrackingApps {
  GoogleFit = 'GoogleFit',
  SamsungHealth = 'SamsungHealth',
  Fitbit = 'Fitbit',
  Runkeeper = 'Runkeeper',
  Peleton = 'Peleton',
  AdidasRunning = 'AdidasRunning',
  MyFitnessPal = 'MyFitnessPal',
  Strava = 'Strava',
  Other = 'Other',
}

export const allTrackingAppsAndroid = [
  TrackingApps.GoogleFit,
  TrackingApps.SamsungHealth,
  TrackingApps.Fitbit,
  TrackingApps.Runkeeper,
  TrackingApps.Peleton,
  TrackingApps.AdidasRunning,
  TrackingApps.MyFitnessPal,
  TrackingApps.Strava,
  TrackingApps.Other,
];

const FRIENDLY_MAPPING_TRACKING_APPS = {
  [TrackingApps.GoogleFit]: 'Google Fit',
  [TrackingApps.SamsungHealth]: 'Samsung Health',
  [TrackingApps.Fitbit]: 'Fitbit',
  [TrackingApps.Runkeeper]: 'Runkeeper',
  [TrackingApps.Peleton]: 'Peleton',
  [TrackingApps.AdidasRunning]: 'Adidas Running',
  [TrackingApps.MyFitnessPal]: 'MyFitnessPal',
  [TrackingApps.Strava]: 'Strava',
  [TrackingApps.Other]: 'App not listed',
} satisfies Record<TrackingApps, string>;

const isTrackingApp = (type: string | undefined): type is TrackingApps =>
  !!type && Object.values(TrackingApps).includes(type as TrackingApps);

export const getTrackingAppDisplayName = (type: string | undefined) =>
  isTrackingApp(type) ? FRIENDLY_MAPPING_TRACKING_APPS[type] : (type ?? 'Unknown');
