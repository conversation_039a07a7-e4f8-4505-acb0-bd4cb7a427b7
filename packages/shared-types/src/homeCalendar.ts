import type {HealthStatsDates, IsoDate, Meal, Workout} from './models';

export enum CalendarViewType {
  DAY = 'day',
  WEEK = 'week',
  MONTH = 'month',
}

export const isCalendarViewType = (type: unknown): type is CalendarViewType =>
  Object.values(CalendarViewType).includes(type as CalendarViewType);

export type CalendarParamsDefined = {
  calendarSelectedDate: IsoDate;
  calendarViewType: CalendarViewType;
  end: IsoDate;
  start: IsoDate;
};
export type CalendarParams = CalendarParamsDefined | undefined;

export type CalendarDayData = {
  date: IsoDate;
  workouts?: Workout[] | undefined;
  meals?: Meal[] | undefined;
  isGoalMet: boolean;
  isStepGoalMet: boolean;
  isMileageGoalMet: boolean;
  stepGoalProgress: number;
  stepGoalProgressStr: string;
  mileageGoalProgress: number;
  mileageGoalProgressStr: string;
  healthStats?: HealthStatsDates | undefined;
};

export type CalendarData = {
  calendarDayData: Map<IsoDate, CalendarDayData>;
};
