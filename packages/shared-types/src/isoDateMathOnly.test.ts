import {getIsoDateDifference} from './isoDateMathOnly';

describe('getIsoDateDifference', () => {
  it('should calculate the difference between two dates as negative', () => {
    const d1 = '2024-12-23';
    const d2 = '2024-12-24';

    const actual = getIsoDateDifference(d1, d2);

    expect(actual).toEqual(-1);
  });

  it('should calculate the difference between two dates as positive', () => {
    const d1 = '2024-12-24';
    const d2 = '2024-12-23';

    const actual = getIsoDateDifference(d1, d2);

    expect(actual).toEqual(1);
  });

  it('should calculate the difference between two dates as zero', () => {
    const d1 = '2024-12-24';
    const d2 = '2024-12-24';

    const actual = getIsoDateDifference(d1, d2);

    expect(actual).toEqual(0);
  });

  it('should calculate the difference between two dates as negative accross month', () => {
    const d1 = '2024-11-23';
    const d2 = '2024-12-24';

    const actual = getIsoDateDifference(d1, d2);

    expect(actual).toEqual(-31);
  });
});
