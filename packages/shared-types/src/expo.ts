import type * as Device from 'expo-device';
import type * as Network from 'expo-network';
import type {Notification, NotificationContentInput} from 'expo-notifications';

export {PermissionStatus} from 'expo-modules-core';
export type {ExpoPushToken} from 'expo-notifications';
export {IosAuthorizationStatus} from 'expo-notifications';

export type DeviceStateType = {
  osName: string | null;
  osVersion: string | null;
  modelName: string | null;
  brand: string | null;
  deviceName: string | null;
  deviceType: Device.DeviceType | null;
  designName: string | null;
  manufacturer: string | null;
  modelId: string | null;
};

export type NetworkStateType = {
  networkType: Network.NetworkStateType | undefined;
  isNetworkConnected: boolean | undefined;
  isInternetReachable: boolean | undefined;
};

export type DeviceInfoType = DeviceStateType & NetworkStateType;
export type ExpoNotification = Notification;
export type SendNotificationRequest = NotificationContentInput[];

export type SendNotificationResponse = {status: string; id: string};
