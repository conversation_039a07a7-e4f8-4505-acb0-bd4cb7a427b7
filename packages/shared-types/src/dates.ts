import {
  fromZonedTime as fromZonedTimeDateFns,
  getTimezoneOffset as getTimezoneOffsetDateFns,
  toZonedTime as toZonedTimeDateFns,
} from 'date-fns-tz';
import {DateTime} from 'luxon';
import type {IsoDate, IsoMonth, IsoTime} from './models';
import {convertLocalTimestamp, isDST} from './datesMathOnly';

// eslint-disable-next-line unicorn/prefer-export-from -- for organization
export {fromZonedTimeDateFns, getTimezoneOffsetDateFns, toZonedTimeDateFns};

const MS_24_HOURS = 24 * 60 * 60 * 1000;
const DEFAULT_TIME_ZONE = 'America/Los_Angeles';

//** TYPES */

export type DateInterval = {
  endDate: Date;
  startDate: Date;
};

export type TimeZones =
  | 'America/New_York'
  | 'America/Chicago'
  | 'America/Denver'
  | 'America/Phoenix' // special timezone
  | 'America/Boise' // special timezone
  | 'America/Los_Angeles'
  | 'Pacific/Honolulu'
  | 'UTC';

export const SUPPORTED_TIMEZONES = [
  'America/New_York',
  'America/Chicago',
  'America/Denver',
  'America/Los_Angeles',
] as const;

type SupportedTimeZonesType = (typeof SUPPORTED_TIMEZONES)[number];

export const isSupportedTimeZone = (timeZone: unknown): timeZone is SupportedTimeZonesType =>
  SUPPORTED_TIMEZONES.includes(timeZone as SupportedTimeZonesType);

const ABBREVIATION_MAP = {
  'America/Los_Angeles': 'PT',
  'America/New_York': 'ET',
  'America/Chicago': 'CT',
  'America/Denver': 'MT',
} as const satisfies Record<SupportedTimeZonesType, string>;

export const getTimeZoneAbbreviation = (timeZone: TimeZones): string =>
  // @ts-expect-error -- allow indexing of any timezone
  // eslint-disable-next-line @typescript-eslint/no-unsafe-return -- allow indexing of any timezone
  ABBREVIATION_MAP[timeZone] ?? timeZone;

//** VALIDATORS HELPERS */

const isValidDateObject = (value: unknown): value is Date =>
  value instanceof Date && !Number.isNaN(value.getTime());

export const parseValidDate = (str: string | undefined) => {
  const date = str ? new Date(str) : new Date();
  const isValid = isValidDateObject(date);
  return isValid ? date : new Date();
};

export const isValidDateString = (value: unknown): value is string =>
  typeof value === 'string' && !Number.isNaN(Date.parse(value));

//** TIMEZONE HELPERS */

const MS_IN_HOUR = 3_600_000;
const TIMEZONE_OFFSETS: Partial<Record<string, {standard: number; daylight: number}>> = {
  'America/New_York': {standard: -5 * MS_IN_HOUR, daylight: -4 * MS_IN_HOUR},
  'America/Chicago': {standard: -6 * MS_IN_HOUR, daylight: -5 * MS_IN_HOUR},
  'America/Denver': {standard: -7 * MS_IN_HOUR, daylight: -6 * MS_IN_HOUR},
  'America/Los_Angeles': {standard: -8 * MS_IN_HOUR, daylight: -7 * MS_IN_HOUR},
  // Additional US Timezones
  'America/Phoenix': {standard: -7 * MS_IN_HOUR, daylight: -7 * MS_IN_HOUR}, // Arizona (no DST)
  'America/Boise': {standard: -7 * MS_IN_HOUR, daylight: -6 * MS_IN_HOUR}, // Mountain Time (Boise follows DST)
  'America/Anchorage': {standard: -9 * MS_IN_HOUR, daylight: -8 * MS_IN_HOUR}, // Alaska Time
  'America/Juneau': {standard: -9 * MS_IN_HOUR, daylight: -8 * MS_IN_HOUR}, // Alaska Time
  'America/Nome': {standard: -9 * MS_IN_HOUR, daylight: -8 * MS_IN_HOUR}, // Alaska Time
  'America/Sitka': {standard: -9 * MS_IN_HOUR, daylight: -8 * MS_IN_HOUR}, // Alaska Time
  'America/Metlakatla': {standard: -8 * MS_IN_HOUR, daylight: -8 * MS_IN_HOUR}, // Alaska (Metlakatla does not follow DST)
  'America/Adak': {standard: -10 * MS_IN_HOUR, daylight: -9 * MS_IN_HOUR}, // Hawaii-Aleutian Time (DST observed)
  'Pacific/Honolulu': {standard: -10 * MS_IN_HOUR, daylight: -10 * MS_IN_HOUR}, // Hawaii (no DST)
  'UTC': {standard: 0, daylight: 0},
} as const;

/**
 * Returns the time zone offset in milliseconds for the specified time zone.
 * This is calculated **statically** using precomputed rules and does NOT rely on Date APIs.
 */
export const getTimezoneOffsetMsStatic = (timeZone: TimeZones, date: Date): number => {
  let offsetValue = TIMEZONE_OFFSETS[timeZone];
  offsetValue ??= TIMEZONE_OFFSETS[DEFAULT_TIME_ZONE];

  const utcMs = date.getTime();
  const standardOffset = offsetValue!.standard;
  const daylightOffset = offsetValue!.daylight;

  // Pass 1 (assume standard)
  const localIgnoringDstMs = utcMs + standardOffset;
  const local1 = convertLocalTimestamp(localIgnoringDstMs);
  const isDaylightTime = isDST(local1.year, local1.month, local1.day, local1.hour, local1.minute);

  // If not daylight time, return standardOffset
  if (!isDaylightTime) return standardOffset;

  // Otherwise, is daylight time, recalc local with daylightOffset
  const localDstMs = utcMs + daylightOffset;
  const local2 = convertLocalTimestamp(localDstMs);
  const isDaylightTime2 = isDST(local2.year, local2.month, local2.day, local2.hour, local2.minute);

  // Should almost always be true, but in the just where it flips, return the offset depending on isDaylightTime2
  return isDaylightTime2 ? daylightOffset : standardOffset;
};

export const getTimezoneOffsetMsLuxon = (timeZone: TimeZones, date: Date): number => {
  // 1) Parse the given JS Date as UTC
  const utcDateTime = DateTime.fromJSDate(date, {zone: 'utc'});
  // 2) Convert to the specified timeZone
  const localDateTime = utcDateTime.setZone(timeZone);
  // 3) Luxon’s offset is in minutes; multiply by 60,000 to get ms
  return localDateTime.offset * 60_000;
};

export const getTimezoneOffsetMs = getTimezoneOffsetMsStatic;

// Pre-compute timezone offset for default timezone
getTimezoneOffsetMs(DEFAULT_TIME_ZONE, new Date());

/**
 * Converts a Date assumed to be in UTC into a “local” Date for the specified time zone.
 * In practice, this returns a JS Date whose internal UTC timestamp is offset forward
 * so that calling date.getHours() etc. reflects local time for `timeZone`.
 */
export const toZonedTime = (utcDate: Date, timeZone: TimeZones) => {
  const offset = getTimezoneOffsetMs(timeZone, utcDate);

  // Apply the offset to the UTC date to get the zoned time
  return new Date(utcDate.getTime() + offset);
};

/**
 * Converts a Date assumed to be local to the specified time zone back into a true UTC Date.
 * This is done by reversing the offset that was applied to represent local time.
 *
 * After this function, the returned Date's internal UTC timestamp should correctly represent
 * the same moment in absolute time that `localDate` represented in `timeZone` local time.
 */
export const fromZonedTime = (localDate: Date, timeZone: TimeZones) => {
  const offset = getTimezoneOffsetMs(timeZone, localDate);

  // Reverse the offset to convert back to UTC
  return new Date(localDate.getTime() - offset);
};

export const getCanonicalTimezone = (timeZone: string): TimeZones => {
  try {
    return new Intl.DateTimeFormat('en-US', {timeZone}).resolvedOptions().timeZone as TimeZones;
  } catch {
    throw new Error(`Invalid timezone: ${timeZone}`);
  }
};

export const isSameDayTz = (date1: Date, date2: Date, timeZone: TimeZones): boolean => {
  const offsetFromUTCInMs = getTimezoneOffsetMs(timeZone, date1);
  return (
    // eslint-disable-next-line no-bitwise, unicorn/prefer-math-trunc -- for speed
    (((date1.getTime() + offsetFromUTCInMs) / MS_24_HOURS) | 0) ===
    // eslint-disable-next-line no-bitwise, unicorn/prefer-math-trunc -- for speed
    (((date2.getTime() + offsetFromUTCInMs) / MS_24_HOURS) | 0)
  );
};

export const getDateFromIsoDate = (isoDate: IsoDate, timeZone: TimeZones, days?: number) => {
  const [year, month, day] = isoDate.split('-').map(Number) as [number, number, number];
  const newDateTime = Date.UTC(year, month - 1, days ?? day, 0, 0, 0, 0);
  return new Date(newDateTime - getTimezoneOffsetDateFns(timeZone, new Date(newDateTime)));
};

export const getDateFromIsoDateLocal = (isoDate: IsoDate, timeZone: TimeZones, days?: number) => {
  const [year, month, day] = isoDate.split('-').map(Number) as [number, number, number];
  const newDateTime = Date.UTC(year, month - 1, days ?? day);
  // return toZonedTime(new Date(newDateTime), timeZone);
  return new Date(newDateTime - getTimezoneOffsetDateFns(timeZone, newDateTime));
};

const getYMDFromDate = (date: Date) => {
  // Extract components to manually format
  const year = date.getUTCFullYear();
  const month = String(date.getUTCMonth() + 1).padStart(2, '0'); // Months are 0-indexed
  const day = String(date.getUTCDate()).padStart(2, '0');

  // Construct the formatted string
  return `${year}-${month}-${day}` as IsoDate;
};

export const getIsoStringFromDate = (date: Date, timeZone: TimeZones): IsoDate => {
  const adjustedDate = toZonedTime(date, timeZone);
  return getYMDFromDate(adjustedDate);
};

export const isValidIsoMonth = (value: unknown): value is IsoMonth => {
  if (typeof value !== 'string') return false;
  const [year, month] = value.split('-').map(Number) as [number, number];
  return !Number.isNaN(year) && !Number.isNaN(month) && year > 0 && month > 0 && month <= 12;
};

export const getIsoMonthStringFromDate = (date: Date, timeZone: TimeZones): IsoMonth => {
  const adjustedDate = new Date(date.getTime() + getTimezoneOffsetMs(timeZone, date));

  // Extract components to manually format
  const year = adjustedDate.getUTCFullYear();
  const month = String(adjustedDate.getUTCMonth() + 1).padStart(2, '0'); // Months are 0-indexed

  // Format and return the adjusted date to "yyyy-MM"
  return `${year}-${month}` as IsoMonth;
};

export const convertDateToStartOfDay = (date: Date, timeZone: TimeZones): Date => {
  // 1) Shift from UTC to "local" (PST, etc.)
  const localDate = toZonedTime(date, timeZone);

  // 2) Force the "local" time to midnight
  //    NOT using setUTCHours since doesn't account for DST
  const isoDate = getYMDFromDate(localDate);
  const start = `${isoDate}T00:00:00.000`;

  // 3) Convert that "local midnight" back to UTC, using DST compliant converter
  return fromZonedTimeDateFns(start, timeZone);
};

export const convertDateToEndOfDay = (date: Date, timeZone: TimeZones): Date => {
  // 1) Shift from UTC to "local" time
  const localDate = toZonedTime(date, timeZone);

  // 2) Force the "local" time to 23:59:59.999
  //    NOT using setUTCHours since doesn't account for DST
  const isoDate = getYMDFromDate(localDate);
  const endOfDayString = `${isoDate}T23:59:59.999`;

  // 3) Convert that "local end-of-day" back to UTC, using DST compliant converter
  return fromZonedTimeDateFns(endOfDayString, timeZone);
};

export const getDateFromMonthStartOfDay = (isoMonth: IsoMonth, timeZone: TimeZones) => {
  const [year, month] = isoMonth.split('-').map(Number) as [number, number];
  const dateWithTime = Date.UTC(year, month - 1, 1, 0, 0, 0, 0);

  // Adjust for the timezone
  return fromZonedTime(new Date(dateWithTime), timeZone);
};

export const getDateFromMonthEndOfDay = (isoMonth: IsoMonth, timeZone: TimeZones) => {
  const [year, month] = isoMonth.split('-').map(Number) as [number, number];

  // Get NEXT month start of month datetime
  const nextMonthDate = Date.UTC(
    year,
    month /* DON'T subtract 1, thereby getting next month*/,
    1,
    0,
    0,
    0,
    0,
  );
  // Subtract 1 ms to get END of previous month datetime
  const dateWithTime = nextMonthDate - 1;

  // Adjust for the timezone
  return fromZonedTime(new Date(dateWithTime), timeZone);
};

export const getDateIntervalFromMonth = (isoMonth: IsoMonth, timeZone: TimeZones) => {
  const startDate = getDateFromMonthStartOfDay(isoMonth, timeZone);
  const endDate = getDateFromMonthEndOfDay(isoMonth, timeZone);
  return {startDate, endDate};
};

export const getIsoTimeFromDate = (date: Date, timeZone: TimeZones): IsoTime => {
  const adjustedDate = toZonedTime(date, timeZone);

  // Extract time components to manually format
  const hours = String(adjustedDate.getUTCHours()).padStart(2, '0');
  const minutes = String(adjustedDate.getUTCMinutes()).padStart(2, '0');
  const seconds = String(adjustedDate.getUTCSeconds()).padStart(2, '0');
  const milliseconds = String(adjustedDate.getUTCMilliseconds()).padStart(3, '0');

  // Construct the formatted string
  return `${hours}:${minutes}:${seconds}.${milliseconds}` as IsoTime;
};

export const getDateFromIsoTime = (isoTime: IsoTime, timeZone: TimeZones): Date => {
  const [hours, minutes, secondsWithMs] = isoTime.split(':') as [string, string, string];
  const [seconds, milliseconds] = secondsWithMs.split('.').map(Number) as [number, number];

  const now = new Date();
  const year = now.getUTCFullYear();
  const month = now.getUTCMonth(); // 0-indexed
  const day = now.getUTCDate();

  const dateWithTime = Date.UTC(
    year,
    month,
    day,
    Number(hours),
    Number(minutes),
    seconds,
    milliseconds,
  );

  // Adjust for the timezone
  return new Date(dateWithTime - getTimezoneOffsetMs(timeZone, now));
};
