import {deleteField, Timestamp as FirebaseClientTimestamp} from 'firebase/firestore';
import type {IsoDate} from './models';
import {getIsoStringFromDate, type TimeZones} from './dates';

export type {User as FirebaseUser} from 'firebase/auth';

export {Timestamp as FirebaseClientTimestamp} from 'firebase/firestore';

export type RawTimestamp = {
  _seconds: number;
  _nanoseconds: number;
};

export type DatabaseExportTimestamp = {
  value: {
    _seconds: number;
    _nanoseconds: number;
  };
};

export const isRawTimestamp = (timestamp: unknown): timestamp is RawTimestamp =>
  typeof timestamp === 'object' &&
  typeof (timestamp as {_seconds: unknown})._seconds === 'number' &&
  typeof (timestamp as {_nanoseconds: unknown})._nanoseconds === 'number';

export const isFirebaseClientTimestamp = (
  timestamp: unknown,
): timestamp is FirebaseClientTimestamp =>
  typeof timestamp === 'object' &&
  typeof (timestamp as {seconds: unknown}).seconds === 'number' &&
  typeof (timestamp as {nanoseconds: unknown}).nanoseconds === 'number';

export const isDatabaseExportTimestamp = (
  timestamp: unknown,
): timestamp is DatabaseExportTimestamp =>
  typeof timestamp === 'object' &&
  typeof (timestamp as DatabaseExportTimestamp).value === 'object' &&
  typeof (timestamp as DatabaseExportTimestamp).value._seconds === 'number' &&
  typeof (timestamp as DatabaseExportTimestamp).value._nanoseconds === 'number';

export const isAnyTimestamp = (
  timestamp: unknown,
): timestamp is RawTimestamp | FirebaseClientTimestamp | DatabaseExportTimestamp =>
  isRawTimestamp(timestamp) ||
  isFirebaseClientTimestamp(timestamp) ||
  isDatabaseExportTimestamp(timestamp);

export const timestampToDate = (
  timestamp: FirebaseClientTimestamp | RawTimestamp | DatabaseExportTimestamp,
): Date => {
  if (!(timestamp as undefined | RawTimestamp)) throw new Error('Timestamp is undefined');
  if (isRawTimestamp(timestamp)) {
    const ms = timestamp._seconds * 1000;
    const msNano = timestamp._nanoseconds / 1_000_000;
    return new Date(ms + msNano);
  }
  if (isFirebaseClientTimestamp(timestamp)) {
    const ms = timestamp.seconds ? timestamp.seconds * 1000 : 0;
    const msNano = timestamp.nanoseconds ? timestamp.nanoseconds / 1_000_000 : 0;
    return new Date(ms + msNano);
  }
  if (isDatabaseExportTimestamp(timestamp)) {
    const ms = timestamp.value._seconds * 1000;
    const msNano = timestamp.value._nanoseconds / 1_000_000;
    return new Date(ms + msNano);
  }
  throw new Error(`Timestamp is not a valid type ${JSON.stringify(timestamp)}`);
};

export const minTimestamp = (t1: RawTimestamp, t2: RawTimestamp) =>
  timestampToDate(t1) < timestampToDate(t2) ? t1 : t2;

export const minDate = (d1: Date, d2: Date) => (d1 < d2 ? d1 : d2);

export const anyTimestampToFirestoreClientTimestamp = (
  timestamp: FirebaseClientTimestamp | RawTimestamp | DatabaseExportTimestamp | Date | string,
) => {
  const asDateString = typeof timestamp === 'string' ? new Date(timestamp) : timestamp;
  const asDate = asDateString instanceof Date ? asDateString : timestampToDate(asDateString);
  return FirebaseClientTimestamp.fromDate(asDate) as unknown as RawTimestamp;
};

export const dateToRawTimestamp = (date: Date | string): RawTimestamp => {
  const asDate = typeof date === 'string' ? new Date(date) : date;
  return {
    _seconds: Math.floor(asDate.getTime() / 1000),
    _nanoseconds: asDate.getMilliseconds() * 1_000_000,
  };
};

export const getIsoStringFromTimestamp = (date: RawTimestamp, timeZone: TimeZones): IsoDate => {
  const dateObj = timestampToDate(date);
  return getIsoStringFromDate(dateObj, timeZone);
};

const transformObject = <T extends Record<string, unknown>>(
  data: T,
  callback: (value: T[keyof T]) => T[keyof T],
): T => {
  const transformedData = {...data};
  Object.keys(data).forEach(key => {
    const typedKey = key as keyof T;
    const currentValue = data[typedKey];
    const newValue = callback(currentValue);

    // Only assign if the value has changed
    if (newValue !== currentValue) {
      transformedData[typedKey] = newValue;
    }
  });
  return transformedData;
};

const createTransformer =
  <T extends Record<string, unknown>, K extends keyof T>(
    callback: (value: T[K]) => T[K],
  ): ((data: T) => T) =>
    (data: T) =>
      transformObject(data, value => callback(value as T[K]));

export const composeTransformers =
  <T extends object>(...transformers: ((data: T) => T)[]): ((data: T) => T) =>
    (data: T) =>
      transformers.reduce((acc, transformer) => transformer(acc), data);

export const transformAnyTimestampsToFirebaseClientTimestamps = createTransformer(
  <T extends Record<string, unknown>>(value: T[keyof T]): T[keyof T] => {
    if (value && isAnyTimestamp(value)) {
      return anyTimestampToFirestoreClientTimestamp(value) as T[keyof T];
    }
    return value;
  },
);

const transformUndefinedNullToDeleteField = createTransformer(
  <T extends Record<string, unknown>>(value: T[keyof T] | undefined | null): T[keyof T] => {
    if (value === undefined || value === null) {
      return deleteField() as T[keyof T];
    }
    return value;
  },
);

const transformEmptyStringToDeleteField = createTransformer(
  <T extends Record<string, unknown>>(value: T[keyof T]): T[keyof T] => {
    if (typeof value === 'string' && value === '') {
      return deleteField() as T[keyof T];
    }
    return value;
  },
);

export const transformAnyTimestampToFirebaseClientTimestampAndRemoveUndefined = <
  T extends Record<string, unknown>,
>() =>
  composeTransformers<T>(
    transformAnyTimestampsToFirebaseClientTimestamps,
    transformUndefinedNullToDeleteField,
    transformEmptyStringToDeleteField,
  );

export const isRawTimestampsEqual = (
  a: RawTimestamp | undefined,
  b: RawTimestamp | undefined,
): boolean => {
  if (a === b) return true;
  if (a === undefined || b === undefined) return false;
  if (!isAnyTimestamp(a) || !isAnyTimestamp(b)) {
    // eslint-disable-next-line no-console -- in types folder
    console.warn('Unexpected non timestamp in isRawTimestampsEqual', {a, b});
    return false;
  }
  return timestampToDate(a).getTime() === timestampToDate(b).getTime();
};

export const getNowAsRawTimestampServer = (): RawTimestamp => dateToRawTimestamp(new Date());

export const getNowAsRawTimestampClient = (): RawTimestamp =>
  FirebaseClientTimestamp.fromDate(new Date()) as unknown as RawTimestamp;
