import type {TimeZones} from './dates';
import type {MovementStreakValues} from './models';
import type {
  IsoTime,
  PushNotificationBase,
  PushNotificationBaseData,
  PushNotificationConfig,
  UUIDString,
} from './models/shared';
import type {EnforceEnumKeysInMap} from './utils';

export enum PushNotificationTypes {
  WORKOUT_CREATED_SCHEDULED = 'workoutCreatedScheduled',
  WORKOUT_CREATED_PAST_COMPLETED = 'workoutCreatedPastCompleted',
  WORKOUT_CREATED_PAST_NOT_COMPLETE = 'workoutCreatedPastNotCompleted',
  WORKOUT_CLIENT_COMPLETED = 'workoutClientCompleted',
  CHALLENGE_GROUP_MILESTONES = 'challengeGroupMilestones',
  CHALLENGE_TEAM_DAILY_GOAL_MET = 'challengeTeamDailyGoalMet',
  CHALLENGE_CHECK_IN = 'challengeCheckIn',
  CHALLENGE_TEAM_POST_CREATED = 'challengeTeamPostCreated',
  CHALLENGE_ALL_TEAMS_POST_CREATED = 'challengeAllTeamsPostCreated',
  CHALLENGE_INVITE = 'challengeInvite',
  CHALLENGE_ENDED = 'challengeEnded',
  CHALLENGE_USER_JOINED_FOR_TRAINER = 'challengeUserJoinedForTrainer',
  CHALLENGE_USER_JOINED_FOR_CAPTAIN = 'challengeUserJoinedForCaptain',
  WELLNESS_BLOG_DAILY = 'dailyWellnessBlog',
  WELLNESS_QUIZ_DAILY = 'dailyWellnessQuiz',
  WELLNESS_QUIZ_STREAK_CONTINUE = 'wellnessQuizStreakContinue',
  WELLNESS_QUIZ_STREAK_REMINDER = 'wellnessQuizStreakReminder',
  /** @deprecated since movement streaks start at, only send reminder continue notifications */
  MOVEMENT_STREAK_START = 'movementStreakStart',
  MOVEMENT_STREAK_CONTINUE = 'movementStreakContinue',
  // MOVEMENT_STREAK_REMINDER = 'movementStreakReminder',
}

export const ALL_PUSH_NOTIFICATION_TYPES = Object.values(PushNotificationTypes);

export const PUSH_NOTIFICATION_DEFAULT_CONFIG: Partial<
  Record<PushNotificationTypes, PushNotificationConfig | undefined>
> = {
  [PushNotificationTypes.WELLNESS_BLOG_DAILY]: {
    timeOfDay: '09:00:00.000', // 9am local
    intervalInMilliseconds: 3 * 24 * 60 * 60 * 1000, // 3 days
  },
  [PushNotificationTypes.WELLNESS_QUIZ_DAILY]: {
    timeOfDay: '09:00:00.000', // 9am local
    intervalInMilliseconds: 1 * 24 * 60 * 60 * 1000, // 1 day
  },
  [PushNotificationTypes.WELLNESS_QUIZ_STREAK_REMINDER]: {
    timeOfDay: '20:00:00.000', // 8pm local
  },
  [PushNotificationTypes.WELLNESS_QUIZ_STREAK_CONTINUE]: {},
  [PushNotificationTypes.MOVEMENT_STREAK_START]: {
    timeOfDay: '08:00:00.000', // 8am local
  },
  [PushNotificationTypes.MOVEMENT_STREAK_CONTINUE]: {
    timeOfDay: '08:00:00.000', // 8am local
  },
  [PushNotificationTypes.WORKOUT_CREATED_SCHEDULED]: undefined,
  [PushNotificationTypes.WORKOUT_CREATED_PAST_COMPLETED]: undefined,
  [PushNotificationTypes.WORKOUT_CREATED_PAST_NOT_COMPLETE]: undefined,
  [PushNotificationTypes.WORKOUT_CLIENT_COMPLETED]: undefined,
  [PushNotificationTypes.CHALLENGE_GROUP_MILESTONES]: undefined,
  [PushNotificationTypes.CHALLENGE_TEAM_DAILY_GOAL_MET]: undefined,
  [PushNotificationTypes.CHALLENGE_CHECK_IN]: undefined,
  [PushNotificationTypes.CHALLENGE_TEAM_POST_CREATED]: undefined,
  [PushNotificationTypes.CHALLENGE_ALL_TEAMS_POST_CREATED]: undefined,
  [PushNotificationTypes.CHALLENGE_INVITE]: undefined,
  [PushNotificationTypes.CHALLENGE_ENDED]: undefined,
  [PushNotificationTypes.CHALLENGE_USER_JOINED_FOR_TRAINER]: undefined,
  [PushNotificationTypes.CHALLENGE_USER_JOINED_FOR_CAPTAIN]: undefined,
} satisfies Record<PushNotificationTypes, PushNotificationConfig | undefined>;

export const DEFAULT_NOTIFICATION_TIME_OF_DAY: IsoTime = '09:00:00.000';

/** WORKOUT CREATED notifications */

export const ALL_WORKOUT_CREATED_NOTIFICATION_TYPES = [
  PushNotificationTypes.WORKOUT_CREATED_SCHEDULED,
  PushNotificationTypes.WORKOUT_CREATED_PAST_COMPLETED,
  PushNotificationTypes.WORKOUT_CREATED_PAST_NOT_COMPLETE,
] as const;

export type PushNotificationWorkoutCreatedTypes =
  (typeof ALL_WORKOUT_CREATED_NOTIFICATION_TYPES)[number];

export type PushNotificationWorkoutCreatedData = PushNotificationBaseData<
  PushNotificationWorkoutCreatedTypes,
  {
    workoutId: UUIDString;
  }
>;

export const isPushNotificationWorkoutCreatedData = (
  data: unknown,
): data is PushNotificationWorkoutCreatedData =>
  !!data &&
  typeof data === 'object' &&
  ALL_WORKOUT_CREATED_NOTIFICATION_TYPES.includes(
    (data as Record<string, unknown>).type as PushNotificationWorkoutCreatedTypes,
  );

/** WORKOUT COMPLETE notifications */

const WORKOUT_COMPLETED_NOTIFICATION_TYPES = [
  PushNotificationTypes.WORKOUT_CLIENT_COMPLETED,
] as const;

type PushNotificationWorkoutCompletedTypes = (typeof WORKOUT_COMPLETED_NOTIFICATION_TYPES)[number];

export type PushNotificationWorkoutCompletedData = PushNotificationBaseData<
  PushNotificationTypes.WORKOUT_CLIENT_COMPLETED,
  {
    parentWorkoutId: UUIDString;
  }
>;

export const isPushNotificationWorkoutCompletedData = (
  data: unknown,
): data is PushNotificationWorkoutCompletedData =>
  !!data &&
  typeof data === 'object' &&
  WORKOUT_COMPLETED_NOTIFICATION_TYPES.includes(
    (data as Record<string, unknown>).type as PushNotificationWorkoutCompletedTypes,
  );

/** CHALLENGE notifications */

export const ALL_CHALLENGE_NOTIFICATION_TYPES = [
  PushNotificationTypes.CHALLENGE_GROUP_MILESTONES,
  PushNotificationTypes.CHALLENGE_CHECK_IN,
  PushNotificationTypes.CHALLENGE_TEAM_POST_CREATED,
  PushNotificationTypes.CHALLENGE_ALL_TEAMS_POST_CREATED,
  PushNotificationTypes.CHALLENGE_TEAM_DAILY_GOAL_MET,
  PushNotificationTypes.CHALLENGE_ENDED,
  PushNotificationTypes.CHALLENGE_INVITE,
  PushNotificationTypes.CHALLENGE_USER_JOINED_FOR_TRAINER,
  PushNotificationTypes.CHALLENGE_USER_JOINED_FOR_CAPTAIN,
] as const;

export const ALL_CHALLENGE_TRAINER_NOTIFICATION_TYPES = [
  PushNotificationTypes.CHALLENGE_USER_JOINED_FOR_TRAINER,
] as const;

export const ALL_CHALLENGE_NON_TRAINER_NOTIFICATION_TYPES = [
  PushNotificationTypes.CHALLENGE_GROUP_MILESTONES,
  PushNotificationTypes.CHALLENGE_CHECK_IN,
  PushNotificationTypes.CHALLENGE_TEAM_POST_CREATED,
  PushNotificationTypes.CHALLENGE_ALL_TEAMS_POST_CREATED,
  PushNotificationTypes.CHALLENGE_TEAM_DAILY_GOAL_MET,
  PushNotificationTypes.CHALLENGE_ENDED,
  PushNotificationTypes.CHALLENGE_INVITE,
  PushNotificationTypes.CHALLENGE_USER_JOINED_FOR_CAPTAIN,
] as const;

export type PushNotificationChallengeTypes = (typeof ALL_CHALLENGE_NOTIFICATION_TYPES)[number];

export type PushNotificationChallengeData = PushNotificationBaseData<
  PushNotificationChallengeTypes,
  {
    challengeId: UUIDString;
  }
>;

export const isPushNotificationChallengeData = (
  data: unknown,
): data is PushNotificationChallengeData =>
  !!data &&
  typeof data === 'object' &&
  ALL_CHALLENGE_NOTIFICATION_TYPES.includes(
    (data as Record<string, unknown>).type as PushNotificationChallengeTypes,
  ) &&
  !!(data as Record<string, unknown>).challengeId;

/** WELLNESS BLOG notifications */

type PusNotificationWellnessBlogData = PushNotificationBaseData<
  PushNotificationTypes.WELLNESS_BLOG_DAILY,
  Record<string, unknown>
>;

export const isPushNotificationWellnessBlogData = (
  data: unknown,
): data is PusNotificationWellnessBlogData =>
  !!data &&
  typeof data === 'object' &&
  (data as Record<string, unknown>).type === PushNotificationTypes.WELLNESS_BLOG_DAILY;

/** WELLNESS QUIZ notifications */

export const ALL_WELLNESS_QUIZ_NOTIFICATION_TYPES = [
  PushNotificationTypes.WELLNESS_QUIZ_DAILY,
  PushNotificationTypes.WELLNESS_QUIZ_STREAK_CONTINUE,
  PushNotificationTypes.WELLNESS_QUIZ_STREAK_REMINDER,
] as const;

export type PushNotificationWellnessQuizTypes =
  (typeof ALL_WELLNESS_QUIZ_NOTIFICATION_TYPES)[number];

export type PushNotificationWellnessQuizData = PushNotificationBaseData<
  PushNotificationWellnessQuizTypes,
  Record<string, unknown>
>;

export const isPushNotificationWellnessQuizData = (
  data: unknown,
): data is PushNotificationWellnessQuizData =>
  !!data &&
  typeof data === 'object' &&
  ALL_WELLNESS_QUIZ_NOTIFICATION_TYPES.includes(
    (data as Record<string, unknown>).type as PushNotificationWellnessQuizTypes,
  );

/** STREAK notifications */

export const ALL_WELLNESS_QUIZ_STREAK_NOTIFICATION_TYPES = [
  PushNotificationTypes.WELLNESS_QUIZ_STREAK_CONTINUE,
  PushNotificationTypes.WELLNESS_QUIZ_STREAK_REMINDER,
] as const;

export const ALL_MOVEMENT_STREAK_NOTIFICATION_TYPES = [
  PushNotificationTypes.MOVEMENT_STREAK_START,
  PushNotificationTypes.MOVEMENT_STREAK_CONTINUE,
] as const;

export const ALL_STREAK_NOTIFICATION_TYPES = [
  ...ALL_WELLNESS_QUIZ_STREAK_NOTIFICATION_TYPES,
  ...ALL_MOVEMENT_STREAK_NOTIFICATION_TYPES,
] as const;

export type PushNotificationMovementStreakTypes =
  (typeof ALL_MOVEMENT_STREAK_NOTIFICATION_TYPES)[number];

type PusNotificationMovementStreakData = PushNotificationBaseData<
  PushNotificationMovementStreakTypes,
  {currentStreakInDays: number}>;

export const isPushNotificationMovementStreakData = (
  data: unknown,
): data is PusNotificationMovementStreakData =>
  !!data &&
  typeof data === 'object' &&
  ALL_MOVEMENT_STREAK_NOTIFICATION_TYPES.includes(
    (data as Record<string, unknown>).type as PushNotificationMovementStreakTypes,
  ) &&
  Number.isInteger((data as Record<string, unknown>).currentStreakInDays);

type PushNotificationInputStreakData = {
  currentStreakInDays: number;
};

type PushNotificationInputWorkoutCreatedData = {
  currentDate: Date;
  timeZone: TimeZones;
  type: PushNotificationWorkoutCreatedTypes;
  workoutId: UUIDString;
  workoutStartDate: Date;
};

type PushNotificationInputMap_ = {
  [PushNotificationTypes.WELLNESS_BLOG_DAILY]: {
    articlePreview: string;
    articleTitle: string;
  };
  [PushNotificationTypes.WELLNESS_QUIZ_DAILY]: {
    question: string;
  };
  [PushNotificationTypes.WELLNESS_QUIZ_STREAK_REMINDER]: PushNotificationInputStreakData;
  [PushNotificationTypes.WELLNESS_QUIZ_STREAK_CONTINUE]: PushNotificationInputStreakData;
  [PushNotificationTypes.MOVEMENT_STREAK_START]: {
    movementStreakValues: MovementStreakValues;
  };
  [PushNotificationTypes.MOVEMENT_STREAK_CONTINUE]: PushNotificationInputStreakData;
  [PushNotificationTypes.WORKOUT_CREATED_SCHEDULED]: PushNotificationInputWorkoutCreatedData;
  [PushNotificationTypes.WORKOUT_CREATED_PAST_COMPLETED]: PushNotificationInputWorkoutCreatedData;
  [PushNotificationTypes.WORKOUT_CREATED_PAST_NOT_COMPLETE]: PushNotificationInputWorkoutCreatedData;
  [PushNotificationTypes.WORKOUT_CLIENT_COMPLETED]: {
    firstName: string;
    lastName: string;
    parentWorkoutId: UUIDString;
    workoutName: string;
  };
  [PushNotificationTypes.CHALLENGE_GROUP_MILESTONES]: {
    groupingType: 'group' | 'team';
    miles: number;
  };
  [PushNotificationTypes.CHALLENGE_TEAM_DAILY_GOAL_MET]: {
    goalMiles: string;
    groupingType: 'team' | 'group';
    teamName: string;
  };
  [PushNotificationTypes.CHALLENGE_CHECK_IN]: Record<string, unknown>;
  [PushNotificationTypes.CHALLENGE_TEAM_POST_CREATED]: {
    authorName: string;
    postBody: string;
  };
  [PushNotificationTypes.CHALLENGE_ALL_TEAMS_POST_CREATED]: {
    authorName: string;
    postBody: string;
  };
  [PushNotificationTypes.CHALLENGE_INVITE]: {
    challengeName: string;
  };
  [PushNotificationTypes.CHALLENGE_ENDED]: {
    challengeName: string;
  };
  [PushNotificationTypes.CHALLENGE_USER_JOINED_FOR_TRAINER]: {
    challengeName: string;
    firstName: string;
    lastName: string;
    teamName: string | undefined;
  };
  [PushNotificationTypes.CHALLENGE_USER_JOINED_FOR_CAPTAIN]: {
    challengeName: string;
    firstName: string;
    lastName: string;
    teamName: string;
  };
};

export type PushNotificationInputMap = EnforceEnumKeysInMap<
  PushNotificationTypes,
  PushNotificationInputMap_
>;

type PushNotificationOutputMap_ = {
  [PushNotificationTypes.WELLNESS_BLOG_DAILY]: PushNotificationBase;
  [PushNotificationTypes.WELLNESS_QUIZ_DAILY]: PushNotificationWellnessQuizData;
  [PushNotificationTypes.WELLNESS_QUIZ_STREAK_REMINDER]: PushNotificationWellnessQuizData;
  [PushNotificationTypes.WELLNESS_QUIZ_STREAK_CONTINUE]: PushNotificationWellnessQuizData;
  [PushNotificationTypes.MOVEMENT_STREAK_START]: PushNotificationBase;
  [PushNotificationTypes.MOVEMENT_STREAK_CONTINUE]: PushNotificationBase;
  [PushNotificationTypes.WORKOUT_CREATED_SCHEDULED]: PushNotificationWorkoutCreatedData;
  [PushNotificationTypes.WORKOUT_CREATED_PAST_COMPLETED]: PushNotificationWorkoutCreatedData;
  [PushNotificationTypes.WORKOUT_CREATED_PAST_NOT_COMPLETE]: PushNotificationWorkoutCreatedData;
  [PushNotificationTypes.WORKOUT_CLIENT_COMPLETED]: PushNotificationWorkoutCompletedData;
  [PushNotificationTypes.CHALLENGE_GROUP_MILESTONES]: PushNotificationChallengeData;
  [PushNotificationTypes.CHALLENGE_TEAM_DAILY_GOAL_MET]: PushNotificationChallengeData;
  [PushNotificationTypes.CHALLENGE_CHECK_IN]: PushNotificationChallengeData;
  [PushNotificationTypes.CHALLENGE_TEAM_POST_CREATED]: PushNotificationChallengeData;
  [PushNotificationTypes.CHALLENGE_ALL_TEAMS_POST_CREATED]: PushNotificationChallengeData;
  [PushNotificationTypes.CHALLENGE_INVITE]: PushNotificationChallengeData;
  [PushNotificationTypes.CHALLENGE_ENDED]: PushNotificationChallengeData;
  [PushNotificationTypes.CHALLENGE_USER_JOINED_FOR_TRAINER]: PushNotificationChallengeData;
  [PushNotificationTypes.CHALLENGE_USER_JOINED_FOR_CAPTAIN]: PushNotificationChallengeData;
};

export type PushNotificationOutputMap = EnforceEnumKeysInMap<
  PushNotificationTypes,
  PushNotificationOutputMap_
>;
