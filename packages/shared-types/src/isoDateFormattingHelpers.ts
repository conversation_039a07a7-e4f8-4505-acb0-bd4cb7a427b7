import {format} from 'date-fns-tz';
import type {IsoDate} from './models';
import {getDateFromIsoDate, type TimeZones} from './dates';
import {getMonthKey} from './isoDateMonthHelpers';
import {type DayIndex, getDayOfWeekIso} from './isoDateWeekHelpers';

const monthNames = [
  'Jan',
  'Feb',
  'Mar',
  'Apr',
  'May',
  'Jun',
  'Jul',
  'Aug',
  'Sep',
  'Oct',
  'Nov',
  'Dec',
];
const suffix = (d: number) =>
  ['th', 'st', 'nd', 'rd'][d % 10 > 3 || [11, 12, 13].includes(d % 100) ? 0 : d % 10];

// Formats strings in the "MMM do" format WITHOUT using date objects
const formatMonthDay = (day: number, month?: number): string =>
  month ? `${monthNames[month - 1]} ${day}${suffix(day)}` : `${day}${suffix(day)}`;

export const formatIsoDateRange = (startDate: IsoDate, endDate: IsoDate): string => {
  const isSameMonth = getMonthKey(startDate) === getMonthKey(endDate);
  const [startYear, startMonth, startDay] = startDate.split('-').map(Number) as [
    number,
    number,
    number,
  ];
  const [_, endMonth, endDay] = endDate.split('-').map(Number) as [number, number, number];
  const start = formatMonthDay(startDay, startMonth);
  const end = isSameMonth ? formatMonthDay(endDay) : formatMonthDay(endDay, endMonth);
  const year = startYear;

  return `${start} - ${end}, ${year}`;
};
const DAY_NAMES_LONG: Record<DayIndex, string> = {
  0: 'Sunday',
  1: 'Monday',
  2: 'Tuesday',
  3: 'Wednesday',
  4: 'Thursday',
  5: 'Friday',
  6: 'Saturday',
};

const DAY_NAMES_SHORT: Record<DayIndex, string> = {
  0: 'Sun',
  1: 'Mon',
  2: 'Tue',
  3: 'Wed',
  4: 'Thur',
  5: 'Fri',
  6: 'Sat',
};
const DAY_NAMES_ABBREVIATED: Record<DayIndex, string> = {
  0: 'Su',
  1: 'Mo',
  2: 'Tu',
  3: 'We',
  4: 'Th',
  5: 'Fr',
  6: 'Sa',
};

export const DAYS_OF_WEEK_LABELS = ['Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa', 'Su'] as const;

// Given an IsoDate, return the corresponding day of the week as a string
export const formatDayOfWeekShort = (isoDate: IsoDate) => {
  const dayOfWeek = getDayOfWeekIso(isoDate);
  return DAY_NAMES_SHORT[dayOfWeek];
};
export const formatDayOfWeekAbbreviated = (isoDate: IsoDate) => {
  const date = getDayOfWeekIso(isoDate);

  return DAY_NAMES_ABBREVIATED[date];
};

export const formatDayOfWeekLong = (isoDate: IsoDate) => {
  const dayOfWeek = getDayOfWeekIso(isoDate);
  return DAY_NAMES_LONG[dayOfWeek];
};

// 'EEE, MM/dd/yyyy'
export const formatDayOfWeekFull = (isoDate: IsoDate) => {
  const [year, month, day] = isoDate.split('-').map(Number) as [number, number, number];
  const dayOfWeek = getDayOfWeekIso(isoDate);
  return `${DAY_NAMES_SHORT[dayOfWeek]}, ${monthNames[month - 1]} ${day}, ${year}`;
};

export const formatMonthDayDate = (date: IsoDate, timeZone: TimeZones) =>
  format(getDateFromIsoDate(date, timeZone), 'MMM d');
