import type {BaseAppUser, ChallengeGroupDocument, UUIDString} from './models';

export type ImportUserDTO = {
  email: string;
  firstName: string;
  lastName: string;
  phoneNumber?: string | undefined;
  organizationId?: UUIDString | undefined;
  groupName?: string | undefined;
  teamName?: string | undefined;
  challengeId?: UUIDString | undefined;
};

export type ImportUserChallengeDTO = BaseAppUser & {
  groupName: string;
  teamName?: string | undefined;
  challengeId: UUIDString;
};

export type ImportUsersRequestDTO = {
  users: ImportUserDTO[];
  isSendWelcomeEmails?: boolean | undefined;
  isImportToChallenges?: boolean | undefined;
  isCreateUserAccounts?: boolean | undefined;
};

export type ImportUserResponseDTO = {
  createUserMessages: string[];
  challengeImportMessages?: string[];
};

export type ChallengeImportUserValidate = {
  challengeId: UUIDString;
  childGroup: ChallengeGroupDocument;
  parentGroup: ChallengeGroupDocument;
  user: ImportUserChallengeDTO;
};
