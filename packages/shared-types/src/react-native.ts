import type {
  GestureResponderEvent,
  LayoutChangeEvent as LayoutChangeEventNative,
  NativeScrollEvent,
  NativeSyntheticEvent,
  TextStyle,
  ViewStyle as ViewStyleNative,
} from 'react-native';

export type ScrollEvent = NativeSyntheticEvent<NativeScrollEvent>;

export type PressEvent = (event: GestureResponderEvent) => void;
export type LayoutChangeEvent = LayoutChangeEventNative;
export type ViewStyle = ViewStyleNative;

export type StyleProp = {
  style?: ViewStyle;
};
export type TextStyleProp = {
  style?: TextStyle;
};
