declare module '@env' {
  export const FIREBASE_API_KEY: string;
  export const FIREBASE_AUTH_DOMAIN: string;
  export const FIREBASE_DATABASE_URL: string;
  export const FIREBASE_MEASUREMENT_ID: string;
  export const FIREBASE_APP_ID: string;
  export const FIREBASE_MESSAGING_SENDER_ID: string;
  export const FIREBASE_STORAGE_BUCKET: string;
  export const FIREBASE_PROJECT_ID: string;
  export const BASE_URL_FIREBASE_API: string;
  export const APP_ENV: string;
  export const LINK_BUNDLE_ID: string;
  export const LINK_PACKAGE_NAME: string;
  export const LINK_DYNAMIC_DOMAIN: string;
  export const EXPO_ACCESS_TOKEN: string;
  export const FITBIT_CLIENT_ID: string;
}

declare global {
  namespace NodeJS {
    // eslint-disable-next-line @typescript-eslint/consistent-type-definitions -- necessary for extending ProcessEnv
    interface ProcessEnv {
      FIREBASE_API_KEY: string;
      FIREBASE_AUTH_DOMAIN: string;
      FIREBASE_DATABASE_URL: string;
      FIREBASE_MEASUREMENT_ID: string;
      FIREBASE_APP_ID: string;
      FIREBASE_MESSAGING_SENDER_ID: string;
      FIREBASE_STORAGE_BUCKET: string;
      FIREBASE_PROJECT_ID: string;
      BASE_URL_FIREBASE_API: string;
      APP_ENV: string;
      LINK_BUNDLE_ID: string;
      LINK_PACKAGE_NAME: string;
      LINK_DYNAMIC_DOMAIN: string;
      EXPO_ACCESS_TOKEN: string;
      FITBIT_CLIENT_ID: string;
    }
  }
}
