/* eslint-disable no-loops/no-loops  -- allowed for date calculations */

const MS = {
  SECOND: 1000,
  MINUTE: 60_000,
  HOUR: 3_600_000,
  DAY: 86_400_000,
};

/**
 * Returns true if "year" is a leap year in Gregorian calendar
 */
const isLeapYear = (year: number) => (year % 4 === 0 && year % 100 !== 0) || year % 400 === 0;

/**
 * Returns number of days in a given year
 */
const daysInYear = (y: number) => (isLeapYear(y) ? 366 : 365);

/**
 * Days in each month for a non-leap year
 */
const daysInMonth = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];

/**
 * Converts a “local ignoring DST” timestamp (ms since epoch) into
 * { year, month, day, hour, minute } (1-based month/day).
 */
export const convertLocalTimestamp = (
  msLocal: number,
): {
  year: number;
  month: number;
  day: number;
  hour: number;
  minute: number;
} => {
  // 1) Separate into whole days vs leftover ms in that day
  let totalDays = Math.floor(msLocal / MS.DAY);
  let leftoverMs = msLocal % MS.DAY;
  if (leftoverMs < 0) {
    leftoverMs += MS.DAY;
    totalDays -= 1;
  }

  // 2) Derive hour and minute
  const hour = Math.floor(leftoverMs / MS.HOUR);
  leftoverMs %= MS.HOUR;
  const minute = Math.floor(leftoverMs / MS.MINUTE);

  // 3) Convert totalDays => year, month, day
  let year = 1970;
  // Move forward or backward year by year until we've accounted for totalDays
  while (totalDays >= daysInYear(year)) {
    totalDays -= daysInYear(year);
    year++;
  }
  while (totalDays < 0) {
    year--;
    totalDays += daysInYear(year);
  }

  // 4) Within the correct year, figure out which month
  let monthIndex = 0;
  // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition -- use break statement
  while (true) {
    let dim = daysInMonth[monthIndex]!;
    if (monthIndex === 1 && isLeapYear(year)) {
      dim = 29; // February in leap year
    }
    if (totalDays < dim) break;
    totalDays -= dim;
    monthIndex++;
  }

  return {
    year,
    month: monthIndex + 1,
    day: totalDays + 1,
    hour,
    minute,
  };
};

/**
 * Returns day of week (0=Sunday,1=Monday,...,6=Saturday) for a given date
 * using Zeller’s Congruence.
 */
const getDayOfWeek = (y: number, m: number, d: number) => {
  if (m < 3) {
    y -= 1;
    m += 12;
  }
  const q = d;
  const k = y % 100;
  const j = Math.floor(y / 100);
  const h =
    (q + Math.floor((13 * (m + 1)) / 5) + k + Math.floor(k / 4) + Math.floor(j / 4) + (5 * j)) % 7;
  return (h + 6) % 7; // Shift so 0=Sunday
};

/**
 * Finds the first Sunday in a given month (assumes day in [1..7]).
 */
const findFirstSunday = (year: number, month: number): number => {
  for (let d = 1; d <= 7; d++) {
    if (getDayOfWeek(year, month, d) === 0) return d;
  }
  return 0; // Should never happen, fallback
};

/**
 * Returns true if the given local date/time is within US DST:
 * second Sunday in March, 2:00 AM -> first Sunday in November, 2:00 AM.
 */
export const isDST = (year: number, month: number, day: number, hour = 0, minute = 0) => {
  // If outside [Mar..Nov], not DST
  if (month < 3 || month > 11) return false;
  // If strictly within [Apr..Oct], always DST
  if (month > 3 && month < 11) return true;

  const minutes = (hour * 60) + minute;

  // --- MARCH boundary ---
  if (month === 3) {
    const firstSunday = findFirstSunday(year, 3);
    const secondSunday = firstSunday + 7;
    if (day < secondSunday) return false;
    if (day > secondSunday) return true;
    return minutes >= 120; // 2:00 AM
  }

  // --- NOVEMBER boundary ---
  if (month === 11) {
    const firstSunday = findFirstSunday(year, 11);
    if (day < firstSunday) return true;
    if (day > firstSunday) return false;
    return minutes < 120; // 2:00 AM
  }

  // Fallback
  return false;
};

/* eslint-enable no-loops/no-loops */
