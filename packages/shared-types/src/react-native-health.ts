export type {
  HealthInputOptions,
  HealthKitPermissions,
  HealthStatusResult,
  HealthValue,
} from 'react-native-health';

// Source: node_modules/react-native-health/index.d.ts
export enum HealthStatusCode {
  NotDetermined = 0,
  SharingDenied = 1,
  SharingAuthorized = 2,
}

export enum HealthPermission {
  ActiveEnergyBurned = 'ActiveEnergyBurned',
  ActivitySummary = 'ActivitySummary',
  AllergyRecord = 'AllergyRecord',
  AppleExerciseTime = 'AppleExerciseTime',
  AppleStandTime = 'AppleStandTime',
  BasalEnergyBurned = 'BasalEnergyBurned',
  BiologicalSex = 'BiologicalSex',
  BloodType = 'BloodType',
  BloodAlcoholContent = 'BloodAlcoholContent',
  BloodGlucose = 'BloodGlucose',
  BloodPressureDiastolic = 'BloodPressureDiastolic',
  BloodPressureSystolic = 'BloodPressureSystolic',
  BodyFatPercentage = 'BodyFatPercentage',
  BodyMass = 'BodyMass',
  BodyMassIndex = 'BodyMassIndex',
  BodyTemperature = 'BodyTemperature',
  DateOfBirth = 'DateOfBirth',
  Biotin = 'Biotin',
  Caffeine = 'Caffeine',
  Calcium = 'Calcium',
  Carbohydrates = 'Carbohydrates',
  Chloride = 'Chloride',
  Cholesterol = 'Cholesterol',
  ConditionRecord = 'ConditionRecord',
  Copper = 'Copper',
  CoverageRecord = 'CoverageRecord',
  EnergyConsumed = 'EnergyConsumed',
  EnvironmentalAudioExposure = 'EnvironmentalAudioExposure',
  FatMonounsaturated = 'FatMonounsaturated',
  FatPolyunsaturated = 'FatPolyunsaturated',
  FatSaturated = 'FatSaturated',
  FatTotal = 'FatTotal',
  Fiber = 'Fiber',
  Folate = 'Folate',
  HeadphoneAudioExposure = 'HeadphoneAudioExposure',
  ImmunizationRecord = 'ImmunizationRecord',
  InsulinDelivery = 'InsulinDelivery',
  Iodine = 'Iodine',
  Iron = 'Iron',
  LabResultRecord = 'LabResultRecord',
  Magnesium = 'Magnesium',
  Manganese = 'Manganese',
  MedicationRecord = 'MedicationRecord',
  Molybdenum = 'Molybdenum',
  Niacin = 'Niacin',
  OxygenSaturation = 'OxygenSaturation',
  PantothenicAcid = 'PantothenicAcid',
  Phosphorus = 'Phosphorus',
  Potassium = 'Potassium',
  ProcedureRecord = 'ProcedureRecord',
  Protein = 'Protein',
  Riboflavin = 'Riboflavin',
  Selenium = 'Selenium',
  Sodium = 'Sodium',
  Sugar = 'Sugar',
  Thiamin = 'Thiamin',
  VitalSignRecord = 'VitalSignRecord',
  VitaminA = 'VitaminA',
  VitaminB12 = 'VitaminB12',
  VitaminB6 = 'VitaminB6',
  VitaminC = 'VitaminC',
  VitaminD = 'VitaminD',
  VitaminE = 'VitaminE',
  VitaminK = 'VitaminK',
  Zinc = 'Zinc',
  Water = 'Water',
  DistanceCycling = 'DistanceCycling',
  DistanceSwimming = 'DistanceSwimming',
  DistanceWalkingRunning = 'DistanceWalkingRunning',
  Electrocardiogram = 'Electrocardiogram',
  FlightsClimbed = 'FlightsClimbed',
  HeartbeatSeries = 'HeartbeatSeries',
  HeartRate = 'HeartRate',
  RestingHeartRate = 'RestingHeartRate',
  HeartRateVariability = 'HeartRateVariability',
  Height = 'Height',
  LeanBodyMass = 'LeanBodyMass',
  MindfulSession = 'MindfulSession',
  NikeFuel = 'NikeFuel',
  PeakFlow = 'PeakFlow',
  RespiratoryRate = 'RespiratoryRate',
  SleepAnalysis = 'SleepAnalysis',
  StepCount = 'StepCount',
  Steps = 'Steps',
  Vo2Max = 'Vo2Max',
  WaistCircumference = 'WaistCircumference',
  WalkingHeartRateAverage = 'WalkingHeartRateAverage',
  Weight = 'Weight',
  Workout = 'Workout',
  WorkoutRoute = 'WorkoutRoute',
  RunningSpeed = 'RunningSpeed',
  RunningPower = 'RunningPower',
  RunningStrideLength = 'RunningStrideLength',
  RunningVerticalOscillation = 'RunningVerticalOscillation',
  RunningGroundContactTime = 'RunningGroundContactTime',
}

const DATA_ORIGIN_DISPLAY_NAME_MAPPING: Record<string, string> = {
  /* eslint-disable @typescript-eslint/naming-convention -- package names */
  'com.sec.android.app.shealth': 'Samsung Health',
  'com.google.android.apps.fitness': 'Google Fit',
  'com.garmin.android.apps.connectmobile': 'Garmin Connect',
  'com.fitbit.FitbitMobile': 'Fitbit',
  'com.nike.ntc': 'Nike Training Club',
  'com.nike.plusgps': 'Nike Run Club',
  'com.runtastic.android': 'Adidas Runtastic',
  'steptracker.healthandfitness.walkingtracker.pedometer': 'Step Tracker Pedometer',
  'com.strava': 'Strava',
  'com.topstep.fitcloudpro': 'FitCloudPro',
  /* eslint-enable @typescript-eslint/naming-convention */
};

export const getDisplayNameOfDataOrigin = (origin: string) =>
  DATA_ORIGIN_DISPLAY_NAME_MAPPING[origin] ?? origin;
