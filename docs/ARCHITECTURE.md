# FlyFit Architecture

This document provides a detailed overview of the FlyFit application architecture, design patterns, and key components.

## FlyFit Domain Diagram

![FlyFit Domain Diagram](./fly-fit-domain-diagram.png)

## Application Architecture

FlyFit follows a modular architecture with clear separation of concerns:

```mermaid
graph TD
    A["UI Layer<br/>(Components)"] --> B["State Layer<br/>(Jotai, Context)"]
    A --> C["Business Logic<br/>(Hooks)"]
    A --> D["Routing<br/>(Navigation)"]
    
    B <--> C
    C --> D
    
    B --> E["Data Layer<br/>(React Query,<br/>Firebase SDK)"]
    C --> E
    
    E --> F["External APIs<br/>(Firebase)"]
```

### Key Layers

1. **UI Layer**: React Native components that render the user interface
2. **State Layer**: Global state management using Jotai and React Context
3. **Business Logic**: Custom hooks that implement business logic
4. **Data Layer**: Data fetching and persistence using React Query and Firebase SDK
5. **External APIs**: Firebase services (Firestore, Auth, Storage, Functions)

## Key Design Patterns

### Component Composition

We use component composition to build complex UIs from smaller, reusable components:

```tsx
/** Example of component composition **/

// src/pages/Challenges/ViewChallengeScreen.tsx
const ViewChallengeContent: React.FC<{challenge: Challenge}> = memoComponent(({challenge}) => (
  <>
    <ChallengeInitializeStage challenge={challenge} />

    {challenge.flags?.isDraft && (
      <StickyAboveNavigation>
        <Box flexDirection='row' justifyContent='flex-end'>
          <Chip icon='file-document-edit-outline' mode='outlined' textStyle={{color: 'red'}}>
            DRAFT STATE
          </Chip>
        </Box>
      </StickyAboveNavigation>
    )}

    <EditGoalModalChallenge />

    {DEV_FEATURE_FLAGS().isDebugViewEnabled && (
      <CopyClipboardWrapper text={challenge.id}>
        <Text>{challenge.id}</Text>
      </CopyClipboardWrapper>
    )}

    <ChallengeHeaderPhoto challenge={challenge} />

    <ChallengeAcceptReject hasHeading hasPb challenge={challenge} />

    <ChallengeProgress
      challenge={challenge}
      header={CONTENT_CODES().CHALLENGE.VIEW.PROGRESS_HEADER}
    />

    <ChallengeCountdown challenge={challenge} />

    <ChallengeLeaderboard challenge={challenge} />
  </>
));
```

### State Management

#### Custom Hooks

We extract all complex state management and data fetching logic into custom hooks:

```tsx
// src/contexts/authContext.ts
export const useIsAuthenticated = () => {
  const hasFirebaseUser = !!useFirebaseUser();
  const hasAppUser = !!useAppUser();
  return hasFirebaseUser && hasAppUser;
};
```

The only time you're NOT creating a custom hook is if the hook

- is never reused anywhere, and will never be reused anywhere else in the future
- is simply wrapping another hook that already has the bulk of the logic you need
- relies heavily on local component state, and passing that state to a custom hook itself would just create unnecessary complexity
- is used directly for configuring the template/UI JSX itself (i.e. the `useAppTheme` hook is a good example)

#### Data Fetching

We use React Query to manage our data fetching from Firebase and external APIs that is used everywhere in the app.

```tsx
/** Firestore query example **/
// src/contexts/firestore/appUserContext.ts
export const useAppUserByEmail = (email: string) => {
  const {data, error, isLoading} = useFirestoreQuery({
    q: () => query(db.appUsers, where('email', '==', email)),
    queryKey: ['useAppUserByEmail', email],
    isEnabled: !!email,
    collectionReference: db.appUsers,
  });
  const appUser = data?.[0];
  return {appUser, isLoading, error};
};
```

```tsx
/** Fetching the daily wellness quiz **/
// src/contexts/firestore/wellnessQuizContext.ts
export const useWellnessQuiz = () => {
  const isoDate = useIsoDateEveryDayChanged();

  return useQuery({
    queryKey: ['useWellnessQuiz', isoDate],
    queryFn: async () => {
      const response = await firebaseApi.getWellnessQuiz(isoDate);
      if (response.status !== StatusCodes.OK_200) {
        throw new Error('Failed to fetch wellness quiz');
      }
      return response.data;
    },
    staleTime: MS_15_MINUTES,
  });
};
```

#### Global State

We use Jotai for global application state:

```tsx
/** Example of Jotai atoms usage **/
// src/contexts/snacks/snackContext.ts
type SnackType = {
  displayText: string;
  duration: number;
  id: string;
};

type SnackContextType = {
  snacks: SnackType[];
};

// NOTE: this is NOT imported directly, only functions wrapping reading/writing to the atom are exported
const snackContextAtom = atom<SnackContextType>({snacks: []});

export const useSnackContext = () => useAtomValue(snackContextAtom);

export const useAddSnack = () => {
  // ...
};

export const useRemoveSnack = () => {
  // ...
};
```

## Firebase Backend

### Firestore Data Model

This is a rough outline of the Firestore data model, last updated 5/26/2025:

```mermaid
erDiagram
    %% Core User Management
    AppUser {
        UUIDString id PK
        UUIDString[] organizationIds FK
    }

    BaseAppUser {
        UUIDString id PK
        UUIDString[] organizationIds FK
    }

    HealthData {
        IsoMonth id PK "yyyy-MM format"
    }

    AppUserNotificationDocument {
        string id PK "always 'default'"
    }

    AppUserMetadata {
        DocumentIdType id PK "healthData|streakDebounce|healthDataDebounce"
    }

    %% Organization Management
    Organization {
        UUIDString id PK
        UUIDString[] adminIds FK
        UUIDString[] coachIds FK
        UUIDString[] clientIds FK
        UUIDString parentOrganizationId FK
        UUIDString[] childOrganizationIds FK
    }

    OrganizationPost {
        UUIDString id PK
        UUIDString organizationId FK
    }

    %% Challenge System
    Challenge {
        UUIDString id PK
        ChallengeGroupingType groupingType "INDIVIDUAL|TEAMS|GROUPS"
        UUIDString[] participantIds FK "for INDIVIDUAL/TEAMS"
        UUIDString rootGroupId FK "for GROUPS only"
    }

    ChallengeGroupDocument {
        UUIDString id PK
        UUIDString challengeId FK
        UUIDString parentGroupId FK
        UUIDString[] groupIds FK
    }

    ChallengeGroupParticipantDocument {
        UUIDString id PK
        UUIDString challengeId FK
        UUIDString[] groupIds FK
        UUIDString teamId FK "for teams challenges"
    }

    %% Challenge Posts
    ChallengePostTeams {
        UUIDString id PK
        UUIDString challengeId FK
        UUIDString teamId FK "null for global posts"
    }

    ChallengePostGroups {
        UUIDString id PK
        UUIDString challengeId FK
        UUIDString groupId FK "null for global posts"
    }

    %% Workouts and Meals
    Workout {
        UUIDString id PK
        UUIDString parentWorkoutId FK
        UUIDString copyWorkoutId FK
        string[] childrenWorkoutIds FK
    }

    Meal {
        UUIDString id PK
        UUIDString copiedMealId FK
    }

    %% Wellness Content
    WellnessBlog {
        string id PK
    }

    WellnessQuiz {
        DocumentIdType id PK
    }

    WellnessQuote {
        string id PK
    }

    %% Streaks and Progress
    MovementStreakDocument {
        SnakeCase2Id id PK "userId_isoDate"
    }

    QuizStreakDocument {
        SnakeCase2Id id PK "userId_isoDate"
        string quizId FK
    }

    %% Reports and Analytics
    SummaryReport {
        UUIDString id PK
        UUIDString creatorOrganizationId FK
        UUIDString challengeId FK
    }

    %% Authentication and Invites
    AuthToken {
        UUIDString userId PK
    }

    InviteCodeDocument {
        InviteCode id PK "short token"
    }

    %% Configuration
    AppConfig {
        string id PK "always 'default'"
    }

    AppUpdateDocument {
        string id PK "ios|android"
    }

    %% Meaningful Relationships Only (no userId links)
    AppUser ||--o{ HealthData : "subcollection"
    AppUser ||--o{ AppUserNotificationDocument : "subcollection"
    AppUser ||--o{ AppUserMetadata : "subcollection"
    AppUser ||--|| AuthToken : "keyed by userId"

    Organization ||--o{ OrganizationPost : "contains posts"
    Organization ||--o{ Organization : "parent/child hierarchy"

    Challenge ||--o{ ChallengeGroupDocument : "contains groups"
    Challenge ||--o{ ChallengeGroupParticipantDocument : "has participants"
    Challenge ||--o{ ChallengePostTeams : "contains team posts"
    Challenge ||--o{ ChallengePostGroups : "contains group posts"

    ChallengeGroupDocument ||--o{ ChallengeGroupDocument : "parent/child hierarchy"

    Workout ||--o{ Workout : "parent/child hierarchy"
    Workout ||--|| Workout : "copied from"

    Meal ||--|| Meal : "copied from"

    WellnessQuiz ||--o{ QuizStreakDocument : "quiz completions"

    Challenge ||--o{ SummaryReport : "generates reports"
    Organization ||--o{ SummaryReport : "org reports"

    BaseAppUser ||--|| AppUser : "algolia search index"
```

Edit using [mermaid.live](https://mermaid.live/edit#pako:eNq1WG1v2zYQ_isEgaIbkLRZHKeJv6Xx1hmbm6xOVmDzMDASLRGmSJWkuipx_vuOFGVJluQXbNEnk7p7-NwL705-woEMKR5hqsaMRIokc4HgefUKXUtF0b2mCk2JIBFNqDDFy6s0dftPxdI-9_eT8cwoJiLEQnT7S9ebP_9CUkVEsEdimBSTUKOfvODzXBQ_3hNNXxD-Z0q4icfEkDr6RMupFCYusNEc5_AcT6doIVVCzBxvoHiCH6VhCxa408YyyKyD6rC6RhhACf-H5Bq9DumCZNy87oOdUkPCDYYl_CS8y1O6hozX5qzgMEqWY_ogMxHQVfWm3GodByG-qfmrFeXGy8NjQcKENYLQkggkCeLtEpw5o_tEUEoUCNw0wr4FLmY8vNmVI3WBW6nNobZvZGH7AHu3YsI5FRFFs1wb6i9dtbvXkWvxD0pmKQi43IjqizmefBxPfp-M769-Xd39eDWdrT58urm_nZXZ0PIReNSwgKWk9DtgwDVAFc5bh9MNgJSUxtFxhnvd4kgkBc9bWdg0ousa7eXzoITpjb_PlYpcjwOiQqAjMZpUbytHvSDrLkIbIIaSpO5tu9YVtO66-FWm2QzXG_bZvTuH8gJhqNMVGee2yKKIywfCUerI9KaI5eV8_yLEonra7scMPPlZqqXMjEZEhGgKVdc70-8fTLTIUq_dTzaQad4tpBvVDsDWYh0pbQkf7kuZMhpa1Z7q9plyLqjWMEIIs-4n5e57LqO-NrmBVar8lrHHXe2wV1UauudxQH3m2mgRzVslIwUQ3lXyq2uOhUTXlZ8JsqTXMMCcgl_KFp1BR5-EfzMtoRm3m7C17H9BrJn2BTB7IvOJplL5ZL0ShOdQwrx9syxJiMoLicNzAkwwUu3ViTsvY53lVWZi8IQfrRzZifjKDPVU7fs7uaR9Y0nhoXZ8C5BrGHi7nF29Xbtax9YXxh7VWUWlWLAoU47meoYrdv_rIJjCCEj3mCqZ1CtwkJIs7KIIt1SA-CLjEHvuiOqYQQG9gWaMvhOydBZnYqm_b873q9XxsXyqD80j65TsIZAQv8BZjTtVtk3IB2Ksx-G99FarWnZYjSXNaYgecm-n1ekYb_2RrdHPIgRQxAgTutkDdqk71aKYv3WVGMWMKqKCOK84VG3YI_RMQw0ark-teeyA6JpSLFpMdH3U2wOuGgoabGw7b3pmO4Tv322LKpCtg-FuR-1yetmZPVK53K66qQh5VlcsWiJaKJlUB7nm6oXd717JRp_zvDo6g9W3xR06cJJy6i7zlmRqVnSrHFEBNkEdRapoBNuyua0OHzZ1xfYXe3kD_XLkil0kOSNIU-tLxERIv80xPsKRYiEeGZXRI5xQ-M62S-wK3RxD_U-gtVmAkKilPe0ZdCBV_5AyKdUg9FGMRwsYumCVFSWz-BdjvQsBDam6hk9gg0eDH4YXDgWPnvA3PDq-fHN59u58OLg4H55cDAZn9PjdEc7x6OTNiX1Oh8OTweD87OJyeHZxdnl5-nyEHx0BOx8-_wtsSI_o) link
