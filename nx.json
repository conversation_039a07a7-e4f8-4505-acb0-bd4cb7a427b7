{"$schema": "./node_modules/nx/schemas/nx-schema.json", "targetDefaults": {"lint": {"inputs": ["{projectRoot}/**/*.ts", "{projectRoot}/**/*.tsx", "{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/eslint.config.mjs", {"externalDependencies": ["eslint"]}], "outputs": ["{projectRoot}/.eslintcache"], "cache": true}, "typecheck": {"inputs": ["{projectRoot}/**/*.ts", "{projectRoot}/**/*.tsx", "{projectRoot}/**/*.js", "{projectRoot}/**/*.cjs", "{projectRoot}/**/*.mjs", "{projectRoot}/tsconfig.json", {"externalDependencies": ["typescript"]}], "outputs": ["{projectRoot}/tsconfig.tsbuildinfo"], "cache": true}, "test:frontend": {"outputs": ["{projectRoot}/.jest"], "cache": true}, "test:backend": {"outputs": ["{projectRoot}/.jest "], "cache": true}, "test": {"outputs": ["{projectRoot}/.jest "], "cache": true}, "test:deprecated": {"outputs": ["{projectRoot}/.jest "], "cache": true}, "test:watch": {"outputs": ["{projectRoot}/.jest "], "cache": true}, "test:coverage": {"outputs": ["{projectRoot}/coverage"], "cache": true}, "test:only": {"outputs": ["{projectRoot}/.jest"], "cache": true}, "test:ci": {"outputs": ["{projectRoot}/.jest"], "cache": true}, "ci:sync": {"outputs": ["{projectRoot}/firestore.indexes.json"], "cache": true}, "env:local:prod": {"outputs": ["{projectRoot}/.env"], "cache": true}, "env:local:dev": {"outputs": ["{projectRoot}/.env"], "cache": true}, "env:ci:prod": {"outputs": ["{projectRoot}/.env"], "cache": true}, "env:ci:dev": {"outputs": ["{projectRoot}/.env"], "cache": true}, "typecheck:ci": {"cache": true}, "typecheck:debug": {"cache": true}, "cloc": {"cache": true}, "ci:sync:deploy": {"cache": true}, "env:local-cli:dev": {"cache": true}, "env:local-cli:prod": {"cache": true}, "build:ci:dev:android": {"cache": true}, "build:ci:dev:ios": {"cache": true}, "build:ci:preview": {"cache": true}, "build:ci:prod:android": {"cache": true}, "build:ci:prod:ios": {"cache": true}, "build:ci:prod:android:submit": {"cache": true}, "build:ci:prod:ios:submit": {"cache": true}, "build:local:dev:ios": {"cache": true}, "build:local:dev:android": {"cache": true}, "build:local:prod:ios": {"cache": true}, "build:local:prod:android": {"cache": true}}, "plugins": [{"plugin": "@nx/eslint/plugin", "options": {"targetName": "eslint:lint"}}, {"plugin": "@nx/jest/plugin", "options": {"targetName": "jest:test"}}, {"plugin": "@nx/expo/plugin", "options": {"startTargetName": "start", "buildTargetName": "build", "prebuildTargetName": "prebuild", "serveTargetName": "serve", "installTargetName": "install", "exportTargetName": "export", "submitTargetName": "submit", "runIosTargetName": "run-ios", "runAndroidTargetName": "run-android", "buildDepsTargetName": "build-deps", "watchDepsTargetName": "watch-deps"}}, {"plugin": "@nx/react-native/plugin", "options": {"startTargetName": "start", "upgradeTargetName": "update", "bundleTargetName": "bundle", "podInstallTargetName": "pod-install", "runIosTargetName": "run-ios", "runAndroidTargetName": "run-android", "buildIosTargetName": "build-ios", "buildAndroidTargetName": "build-android", "syncDepsTargetName": "sync-deps"}}, {"plugin": "@nx/vite/plugin", "options": {"buildTargetName": "build", "testTargetName": "test", "serveTargetName": "serve", "devTargetName": "dev", "previewTargetName": "preview", "serveStaticTargetName": "serve-static", "typecheckTargetName": "typecheck", "buildDepsTargetName": "build-deps", "watchDepsTargetName": "watch-deps"}}], "nxCloudId": "6834f022b107be587ef31f3c"}